{% extends 'estoque/base.html' %}
{% load estoque_extras %}

{% block extra_css %}
<style>
    /* Estilos personalizados para os badges de status */
    .badge.bg-warning {
        background-color: #ffc107 !important;
        color: #000 !important;
    }

    .badge.bg-primary {
        background-color: #0d6efd !important;
        color: #fff !important;
    }
</style>
{% endblock %}

{% block title %}Detalhes da Ordem de Fabricação - Molas Rios{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="mt-4">{{ planejamento.nome }}</h1>
        <div>
            <a href="{% url 'planejamento-list' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Voltar
            </a>
            <a href="{% url 'planejamento-update' planejamento.id %}" class="btn btn-warning">
                <i class="fas fa-edit"></i> Editar
            </a>
            {% if planejamento.status == 'P' %}
                <a href="{% url 'item-planejamento-iniciar' planejamento.itens.first.id %}" class="btn btn-info">
                    <i class="fas fa-play"></i> Produção Iniciada
                </a>
            {% endif %}
            {% if planejamento.status == 'P' or planejamento.status == 'E' or planejamento.status == 'A' %}
                <a href="{% url 'finalizar-ordem-fabricacao' planejamento.id %}" class="btn btn-success">
                    <i class="fas fa-check"></i> Finalizar Ordem
                </a>
                <a href="{% url 'planejamento-cancelar' planejamento.id %}" class="btn btn-danger" onclick="return confirm('Confirma o cancelamento desta ordem de fabricação?')">
                    <i class="fas fa-ban"></i> Cancelar
                </a>
            {% endif %}
        </div>
    </div>

    <!-- Informações da Ordem de Fabricação -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Informações da Ordem de Fabricação</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Status:</strong>
                                {% if planejamento.status == 'P' %}
                                    <span class="badge pendente">Pendente</span>
                                {% elif planejamento.status == 'E' %}
                                    <span class="badge em-andamento">Em Andamento</span>
                                {% elif planejamento.status == 'C' %}
                                    <span class="badge concluido">Concluído</span>
                                {% elif planejamento.status == 'A' %}
                                    <span class="badge atrasado">Atrasado</span>
                                {% elif planejamento.status == 'X' %}
                                    <span class="badge cancelado">Cancelado</span>
                                {% endif %}
                            </p>
                            <p><strong>Prioridade:</strong>
                                {% if planejamento.prioridade == 1 %}
                                    <span class="badge bg-secondary">Baixa</span>
                                {% elif planejamento.prioridade == 2 %}
                                    <span class="badge bg-primary">Normal</span>
                                {% elif planejamento.prioridade == 3 %}
                                    <span class="badge bg-warning">Alta</span>
                                {% elif planejamento.prioridade == 4 %}
                                    <span class="badge bg-danger">Urgente</span>
                                {% endif %}
                            </p>
                            <p><strong>Data de Criação:</strong> {{ planejamento.data_criacao|date:"d/m/Y H:i" }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Data de Início:</strong>
                                {% if planejamento.status == 'P' %}
                                    -
                                {% else %}
                                    {{ planejamento.data_inicio|date:"d/m/Y" }}
                                {% endif %}
                            </p>
                            <p><strong>Data do Fim:</strong> {{ planejamento.data_fim_real|date:"d/m/Y"|default:"Não concluído" }}</p>
                        </div>
                    </div>

                    {% if planejamento.descricao %}
                        <div class="mt-3">
                            <h6 class="font-weight-bold">Descrição:</h6>
                            <p>{{ planejamento.descricao }}</p>
                        </div>
                    {% endif %}

                    {% if planejamento.observacoes %}
                        <div class="mt-3">
                            <h6 class="font-weight-bold">Observações:</h6>
                            <p>{{ planejamento.observacoes }}</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Necessidade de Materiais</h6>
                </div>
                <div class="card-body">
                    {% if necessidades %}
                        <div class="table-responsive">
                            <table class="table table-bordered table-sm">
                                <thead>
                                    <tr>
                                        <th>Material</th>
                                        <th>Necessário</th>
                                        <th>Disponível</th>
                                        <th>Faltante</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for material_id, info in necessidades.items %}
                                        <tr>
                                            <td>{{ info.material.nome }}</td>
                                            <td>{{ info.quantidade|floatformat:2 }} kg</td>
                                            <td>{{ info.disponivel|floatformat:2 }} kg</td>
                                            <td>
                                                {% if info.faltante > 0 %}
                                                    <span class="text-danger">{{ info.faltante|floatformat:2 }} kg</span>
                                                {% else %}
                                                    <span class="text-success">0 kg</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> Nenhum material necessário.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Itens da Ordem de Fabricação -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Itens da Ordem de Fabricação</h6>
        </div>
        <div class="card-body">
            {% if planejamento.itens.all %}
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>Mola</th>
                                <th>Quantidade</th>
                                <th>Produzido</th>
                                <th>Status</th>
                                <th>Prioridade</th>
                                <th>Ordem de Fabricação Nº</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in planejamento.itens.all %}
                                <tr>
                                    <td>
                                        {{ item.mola.codigo }}
                                        <br>
                                        <small class="text-muted">
                                            Material:
                                            {% if planejamento.material %}
                                                {{ planejamento.material.nome }} - {{ planejamento.material.diametro }}
                                            {% elif item.mola.material %}
                                                {{ item.mola.material.nome }} - {{ item.mola.material.diametro }}
                                            {% else %}
                                                Não especificado
                                            {% endif %}
                                        </small>
                                    </td>
                                    <td>{{ item.quantidade }}</td>
                                    <td>{{ item.quantidade_produzida }}</td>
                                    <td>
                                        {% if item.status == 'P' %}
                                            <span class="badge pendente">Pendente</span>
                                        {% elif item.status == 'E' %}
                                            <span class="badge em-andamento">Em Andamento</span>
                                        {% elif item.status == 'C' %}
                                            <span class="badge concluido">Concluído</span>
                                        {% elif item.status == 'X' %}
                                            <span class="badge cancelado">Cancelado</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if item.prioridade == 1 %}
                                            <span class="badge bg-secondary">Baixa</span>
                                        {% elif item.prioridade == 2 %}
                                            <span class="badge bg-primary">Normal</span>
                                        {% elif item.prioridade == 3 %}
                                            <span class="badge bg-warning">Alta</span>
                                        {% elif item.prioridade == 4 %}
                                            <span class="badge bg-danger">Urgente</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ item.origem|extract_number }}</td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{% url 'item-planejamento-update' item.id %}" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="Editar">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{% url 'item-planejamento-delete' item.id %}" class="btn btn-sm btn-danger" data-bs-toggle="tooltip" title="Excluir">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                            {% if item.status == 'E' %}
                                                <button type="button" class="btn btn-sm btn-success" data-bs-toggle="modal" data-bs-target="#registrarProducaoModal{{ item.id }}" title="Registrar Produção">
                                                    <i class="fas fa-plus-circle"></i>
                                                </button>
                                            {% endif %}
                                        </div>

                                        <!-- Modal para Registrar Produção -->
                                        <div class="modal fade" id="registrarProducaoModal{{ item.id }}" tabindex="-1" aria-labelledby="registrarProducaoModalLabel{{ item.id }}" aria-hidden="true">
                                            <div class="modal-dialog">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title" id="registrarProducaoModalLabel{{ item.id }}">Registrar Produção - {{ item.mola.codigo }}</h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                    </div>
                                                    <form action="{% url 'item-planejamento-registrar' item.id %}" method="post">
                                                        {% csrf_token %}
                                                        <div class="modal-body">
                                                            <div class="mb-3">
                                                                <label for="quantidade{{ item.id }}" class="form-label">Quantidade Produzida</label>
                                                                <input type="number" class="form-control" id="quantidade{{ item.id }}" name="quantidade" min="1" max="{{ item.quantidade }}" required>
                                                                <div class="form-text">Máximo: <span id="maximo{{ item.id }}">{{ item.quantidade }}</span></div>
                                                            </div>
                                                        </div>
                                                        <div class="modal-footer">
                                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                                                            <button type="submit" class="btn btn-success">Registrar</button>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> Nenhum item adicionado à ordem de fabricação.
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Calcular quantidades máximas para cada item
    document.addEventListener('DOMContentLoaded', function() {
        {% for item in planejamento.itens.all %}
            var quantidade = {{ item.quantidade }};
            var produzida = {{ item.quantidade_produzida }};
            var maximo = quantidade - produzida;

            // Atualizar o texto e o atributo max
            document.getElementById('maximo{{ item.id }}').textContent = maximo;
            document.getElementById('quantidade{{ item.id }}').setAttribute('max', maximo);
        {% endfor %}
    });

    // Garantir que as cores dos badges sejam aplicadas
    document.addEventListener('DOMContentLoaded', function() {
        // Aplicar cores aos badges de status
        document.querySelectorAll('.badge.pendente').forEach(function(badge) {
            badge.style.backgroundColor = '#ffc107';
            badge.style.color = '#000';
        });

        document.querySelectorAll('.badge.em-andamento').forEach(function(badge) {
            badge.style.backgroundColor = '#0d6efd';
            badge.style.color = '#fff';
        });

        document.querySelectorAll('.badge.concluido').forEach(function(badge) {
            badge.style.backgroundColor = '#198754';
            badge.style.color = '#fff';
        });

        document.querySelectorAll('.badge.atrasado').forEach(function(badge) {
            badge.style.backgroundColor = '#dc3545';
            badge.style.color = '#fff';
        });

        document.querySelectorAll('.badge.cancelado').forEach(function(badge) {
            badge.style.backgroundColor = '#dc3545';
            badge.style.color = '#fff';
        });
    });
</script>
{% endblock %}
