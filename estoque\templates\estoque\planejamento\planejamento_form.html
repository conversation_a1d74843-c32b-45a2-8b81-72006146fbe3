{% extends 'estoque/base.html' %}

{% block title %}
    {% if form.instance.id %}
        Editar Ordem de Fabricação - Molas Rios
    {% else %}
        Nova Ordem de Fabricação - Molas Rios
    {% endif %}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="mt-4">
            {% if form.instance.id %}
                Editar Ordem de Fabricação
            {% else %}
                Nova Ordem de Fabricação
            {% endif %}
        </h1>
        <div>
            <a href="{% if form.instance.id %}{% url 'planejamento-detail' form.instance.id %}{% else %}{% url 'planejamento-list' %}{% endif %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Voltar
            </a>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                {% if form.instance.id %}
                    Editar Ordem de Fabricação
                {% else %}
                    Nova Ordem de Fabricação
                {% endif %}
            </h6>
        </div>
        <div class="card-body">
            <form method="post">
                {% csrf_token %}

                {% if form.non_field_errors %}
                    <div class="alert alert-danger">
                        {% for error in form.non_field_errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                {% endif %}

                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="{{ form.ordem_fabricacao_numero.id_for_label }}" class="form-label">{{ form.ordem_fabricacao_numero.label }}</label>
                            {{ form.ordem_fabricacao_numero }}
                            {% if form.ordem_fabricacao_numero.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.ordem_fabricacao_numero.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="{{ form.data_inicio.id_for_label }}" class="form-label">{{ form.data_inicio.label }}</label>
                            {{ form.data_inicio }}
                            {% if form.data_inicio.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.data_inicio.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="{{ form.mola.id_for_label }}" class="form-label">{{ form.mola.label }}</label>
                            {{ form.mola }}
                            {% if form.mola.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.mola.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="{{ form.material.id_for_label }}" class="form-label">{{ form.material.label }}</label>
                            {{ form.material }}
                            {% if form.material.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.material.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">Serão mostrados apenas materiais disponíveis no estoque que são variantes do material padrão da mola. O campo não é obrigatório.</div>
                        </div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="{{ form.quantidade_produzir.id_for_label }}" class="form-label">{{ form.quantidade_produzir.label }}</label>
                            {{ form.quantidade_produzir }}
                            {% if form.quantidade_produzir.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.quantidade_produzir.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="{{ form.status.id_for_label }}" class="form-label">{{ form.status.label }}</label>
                            {{ form.status }}
                            {% if form.status.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.status.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="{{ form.observacoes.id_for_label }}" class="form-label">{{ form.observacoes.label }}</label>
                    {{ form.observacoes }}
                    {% if form.observacoes.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.observacoes.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>

                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Salvar
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .form-control, .form-select {
        margin-bottom: 0;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // Função para extrair valor numérico de uma string de diâmetro
    function extractNumericValue(diameterStr) {
        if (!diameterStr) return 0;

        // Substituir vírgulas por pontos para garantir formato decimal correto
        diameterStr = diameterStr.replace(',', '.');

        // Remover caracteres não numéricos e manter apenas números e pontos
        const numericStr = diameterStr.replace(/[^\d.]/g, '');

        // Converter para número
        const numericValue = parseFloat(numericStr);

        // Retornar 0 se não for um número válido
        return isNaN(numericValue) ? 0 : numericValue;
    }

    document.addEventListener('DOMContentLoaded', function() {
        // Definir o foco no campo de número da ordem de fabricação ao carregar a página
        document.getElementById('id_ordem_fabricacao_numero').focus();

        // Adicionar classes Bootstrap aos campos do formulário
        const formControls = document.querySelectorAll('input, select, textarea');
        formControls.forEach(function(element) {
            if (element.type !== 'hidden' && element.type !== 'submit') {
                if (element.tagName === 'SELECT') {
                    element.classList.add('form-select');
                } else {
                    element.classList.add('form-control');
                }
            }
        });

        // Adicionar evento para atualizar o campo de material quando a mola é alterada
        const molaSelect = document.getElementById('id_mola');
        const materialSelect = document.getElementById('id_material');

        // Armazenar as opções originais do select de materiais
        const materiaisOriginais = Array.from(materialSelect.options);

        // Ordenar as opções originais por nome e diâmetro
        materiaisOriginais.sort((a, b) => {
            // Ignorar a opção vazia
            if (!a.value || !b.value) return -1;

            // Extrair nome e diâmetro do texto da opção
            const textoA = a.textContent || '';
            const textoB = b.textContent || '';

            // Tentar extrair nome e diâmetro
            const partsA = textoA.split(' - ');
            const partsB = textoB.split(' - ');

            const nomeA = partsA[0] || '';
            const nomeB = partsB[0] || '';

            // Se os nomes são diferentes, ordenar por nome
            if (nomeA !== nomeB) {
                return nomeA.localeCompare(nomeB);
            }

            // Se os nomes são iguais, tentar extrair e ordenar por diâmetro
            const diametroA = partsA.length > 1 ? extractNumericValue(partsA[1]) : 0;
            const diametroB = partsB.length > 1 ? extractNumericValue(partsB[1]) : 0;

            return diametroA - diametroB;
        });

        // Verificar se há opções no select de materiais
        if (materiaisOriginais.length === 0) {
            console.error("Nenhuma opção encontrada no select de materiais!");
        } else {
            console.log(`Total de opções originais: ${materiaisOriginais.length}`);
        }

        // Função para obter materiais compatíveis com a mola selecionada
        function obterMateriaisCompativeis(molaId) {
            // Desabilitar o campo de material enquanto carrega
            materialSelect.disabled = true;

            // Fazer requisição AJAX para obter informações sobre a mola
            const url = `/planejamento/get_material_by_mola/${molaId}/`;

            fetch(url)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`Erro HTTP: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        // Limpar as opções atuais
                        materialSelect.innerHTML = '';

                        // Adicionar opção vazia
                        const emptyOption = document.createElement('option');
                        emptyOption.value = '';
                        emptyOption.textContent = '---------';
                        materialSelect.appendChild(emptyOption);

                        // Verificar se há materiais compatíveis
                        if (data.materiais.length > 0) {
                            // Adicionar materiais compatíveis
                            data.materiais.forEach(material => {
                                const option = document.createElement('option');
                                option.value = material.id;
                                option.textContent = material.nome;
                                materialSelect.appendChild(option);
                            });

                            // Remover qualquer alerta existente
                            const alertaExistente = document.getElementById('alerta-material');
                            if (alertaExistente) {
                                alertaExistente.remove();
                            }

                            // Se houver um material específico para a mola, selecioná-lo automaticamente
                            if (data.material_mola_id) {
                                materialSelect.value = data.material_mola_id;
                            }
                        } else {
                            // Se não houver materiais compatíveis, mostrar todos os materiais ativos
                            resetarMateriais();

                            // Mostrar alerta informativo
                            mostrarAlertaMaterial();
                        }
                    } else {
                        // Em caso de erro, mostrar todos os materiais
                        resetarMateriais();
                        mostrarAlertaMaterial();
                    }

                    // Reativar o campo de material
                    materialSelect.disabled = false;
                })
                .catch(error => {
                    // Em caso de erro, mostrar todos os materiais
                    resetarMateriais();
                    mostrarAlertaMaterial();

                    // Reativar o campo de material
                    materialSelect.disabled = false;
                });
        }

        // Função para mostrar alerta de material
        function mostrarAlertaMaterial() {
            // Remover qualquer alerta existente
            const alertaExistente = document.getElementById('alerta-material');
            if (alertaExistente) {
                alertaExistente.remove();
            }

            // Criar novo alerta
            const alertaDiv = document.createElement('div');
            alertaDiv.id = 'alerta-material';
            alertaDiv.className = 'alert alert-warning mt-2';
            alertaDiv.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Não foram encontrados materiais disponíveis no estoque para esta mola. Verifique se há materiais do mesmo tipo padrão da mola em estoque.';
            materialSelect.parentNode.appendChild(alertaDiv);
        }

        // Função para resetar o select de materiais para o estado original
        function resetarMateriais() {
            // Limpar as opções atuais
            materialSelect.innerHTML = '';

            // Adicionar opção vazia
            const emptyOption = document.createElement('option');
            emptyOption.value = '';
            emptyOption.textContent = '---------';
            materialSelect.appendChild(emptyOption);

            // Restaurar todas as opções originais (já ordenadas)
            materiaisOriginais.forEach(option => {
                if (option.value) { // Não adicionar a opção vazia novamente
                    materialSelect.appendChild(option.cloneNode(true));
                }
            });
        }

        if (molaSelect && materialSelect) {
            // Adicionar evento para quando a mola é alterada
            molaSelect.addEventListener('change', function() {
                const molaId = this.value;

                if (!molaId) {
                    // Se nenhuma mola for selecionada, resetar para todos os materiais
                    resetarMateriais();
                    return;
                }

                // Obter materiais compatíveis com a mola selecionada
                obterMateriaisCompativeis(molaId);
            });

            // Verificar se já existe uma mola selecionada ao carregar a página
            if (molaSelect.value) {
                obterMateriaisCompativeis(molaSelect.value);
            }
        }
    });
</script>
{% endblock %}
