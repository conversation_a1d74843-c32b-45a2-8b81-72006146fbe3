{% extends 'estoque/base.html' %}

{% block title %}Análise de Obsolescência - Controle de Estoque{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Análise de Obsolescência</h1>
        <div>
            <a href="{% url 'gerar-analise-obsolescencia' %}" class="btn btn-success">
                <i class="fas fa-sync"></i> Gerar Nova Análise
            </a>
        </div>
    </div>

    <!-- Formul<PERSON>rio de Filtros -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Filtros</h6>
        </div>
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-4">
                    <label for="cliente" class="form-label">Cliente</label>
                    <input type="text" name="cliente" id="cliente" class="form-control"
                           value="{{ request.GET.cliente|default:'' }}"
                           placeholder="Buscar por cliente...">
                </div>

                <div class="col-md-4">
                    <label for="codigo" class="form-label">Código da Mola</label>
                    <input type="text" name="codigo" id="codigo" class="form-control"
                           value="{{ request.GET.codigo|default:'' }}"
                           placeholder="Buscar por código...">
                </div>

                <div class="col-md-4 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search"></i> Filtrar
                    </button>
                    <a href="{% url 'analise-obsolescencia-list' %}" class="btn btn-secondary me-2">
                        <i class="fas fa-times"></i> Limpar
                    </a>
                </div>

                <div class="col-12 mt-3">
                    <div class="float-end">
                        <button type="submit" name="formato" value="pdf" class="btn btn-danger me-2">
                            <i class="fas fa-file-pdf"></i> Exportar PDF
                        </button>
                        <button type="submit" name="formato" value="csv" class="btn btn-success">
                            <i class="fas fa-file-csv"></i> Exportar CSV
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold">Resumo por Classificação</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for classificacao, info in resumo.items %}
                            <div class="col-md-2 mb-3">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title">{{ info.nome }}</h5>
                                        <p class="card-text display-4">{{ info.count }}</p>
                                        <a href="?classificacao={{ classificacao }}" class="btn btn-sm btn-primary">Ver Itens</a>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card shadow">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold">
                {% if classificacao_atual %}
                    Itens com Classificação:
                    {% for classificacao, info in resumo.items %}
                        {% if classificacao == classificacao_atual %}
                            {{ info.nome }}
                        {% endif %}
                    {% endfor %}
                {% else %}
                    Todos os Itens
                {% endif %}
            </h6>
            {% if classificacao_atual %}
                <a href="{% url 'analise-obsolescencia-list' %}" class="btn btn-sm btn-secondary">
                    <i class="fas fa-times"></i> Limpar Filtro
                </a>
            {% endif %}
        </div>
        <div class="card-body">
            {% if analises %}
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>Código</th>
                                <th>Cliente</th>
                                <th>Classificação</th>
                                <th>Dias Sem Movimentação</th>
                                <th>Última Movimentação</th>
                                <th>Estoque</th>
                                <th>Valor em Estoque</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for analise in analises %}
                                <tr>
                                    <td>{{ analise.mola.codigo }}</td>
                                    <td>{{ analise.mola.cliente }}</td>
                                    <td>
                                        {% if analise.classificacao == 'A' %}
                                            <span class="badge bg-success">{{ analise.get_classificacao_display }}</span>
                                        {% elif analise.classificacao == 'B' %}
                                            <span class="badge bg-info">{{ analise.get_classificacao_display }}</span>
                                        {% elif analise.classificacao == 'C' %}
                                            <span class="badge bg-warning">{{ analise.get_classificacao_display }}</span>
                                        {% elif analise.classificacao == 'D' %}
                                            <span class="badge bg-secondary">{{ analise.get_classificacao_display }}</span>
                                        {% else %}
                                            <span class="badge bg-danger">{{ analise.get_classificacao_display }}</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ analise.dias_sem_movimentacao }}</td>
                                    <td>{{ analise.ultima_movimentacao|date:"d/m/Y"|default:"Nunca" }}</td>
                                    <td>{{ analise.mola.quantidade_estoque }}</td>
                                    <td>R$ {{ analise.valor_estoque|floatformat:2 }}</td>
                                    <td>
                                        <a href="{% url 'analise-obsolescencia-detail' analise.id %}" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{% url 'mola-detail' analise.mola.id %}" class="btn btn-sm btn-primary">
                                            <i class="fas fa-cog"></i>
                                        </a>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Paginação -->
                {% if is_paginated %}
                    <nav aria-label="Paginação">
                        <ul class="pagination justify-content-center mt-4">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1{% if classificacao_atual %}&classificacao={{ classificacao_atual }}{% endif %}" aria-label="Primeira">
                                        <span aria-hidden="true">&laquo;&laquo;</span>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if classificacao_atual %}&classificacao={{ classificacao_atual }}{% endif %}" aria-label="Anterior">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                            {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Primeira">
                                        <span aria-hidden="true">&laquo;&laquo;</span>
                                    </a>
                                </li>
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Anterior">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                            {% endif %}

                            {% for num in page_obj.paginator.page_range %}
                                {% if page_obj.number == num %}
                                    <li class="page-item active">
                                        <a class="page-link" href="#">{{ num }}</a>
                                    </li>
                                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ num }}{% if classificacao_atual %}&classificacao={{ classificacao_atual }}{% endif %}">{{ num }}</a>
                                    </li>
                                {% endif %}
                            {% endfor %}

                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if classificacao_atual %}&classificacao={{ classificacao_atual }}{% endif %}" aria-label="Próxima">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if classificacao_atual %}&classificacao={{ classificacao_atual }}{% endif %}" aria-label="Última">
                                        <span aria-hidden="true">&raquo;&raquo;</span>
                                    </a>
                                </li>
                            {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Próxima">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Última">
                                        <span aria-hidden="true">&raquo;&raquo;</span>
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                {% endif %}
            {% else %}
                <div class="alert alert-info alert-dismissible fade show">
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fechar"></button>
                    <i class="fas fa-info-circle"></i> Nenhuma análise de obsolescência encontrada.
                </div>
            {% endif %}
        </div>
    </div>
</div>
<style>
    @media print {
        .navbar, .sidebar, .footer, .btn, .pagination {
            display: none !important;
        }

        .card {
            border: none !important;
            box-shadow: none !important;
        }

        .card-header {
            background-color: white !important;
            color: black !important;
            border-bottom: 1px solid #ddd !important;
        }

        body {
            background-color: white !important;
            color: black !important;
        }

        .table {
            color: black !important;
        }

        .badge {
            border: 1px solid #ddd !important;
        }

        .badge.bg-success {
            background-color: white !important;
            color: black !important;
            border-color: #28a745 !important;
        }

        .badge.bg-info {
            background-color: white !important;
            color: black !important;
            border-color: #17a2b8 !important;
        }

        .badge.bg-warning {
            background-color: white !important;
            color: black !important;
            border-color: #ffc107 !important;
        }

        .badge.bg-secondary {
            background-color: white !important;
            color: black !important;
            border-color: #6c757d !important;
        }

        .badge.bg-danger {
            background-color: white !important;
            color: black !important;
            border-color: #dc3545 !important;
        }

        @page {
            size: landscape;
        }
    }
</style>
{% endblock %}