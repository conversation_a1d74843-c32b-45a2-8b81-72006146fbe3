{% extends 'estoque/base.html' %}

{% block title %}Nova Movimentação de Material - Molas Rios{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Nova Movimentação de Material</h1>
        <a href="{% url 'movimentacao-material-list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Voltar
        </a>
    </div>

    <div class="card shadow">
        <div class="card-body">
            <form method="post" class="row g-3">
                {% csrf_token %}

                <div class="col-md-6">
                    <label for="{{ form.material.id_for_label }}" class="form-label">Material *</label>
                    {{ form.material.errors }}
                    <select name="{{ form.material.name }}" id="{{ form.material.id_for_label }}" class="form-select {% if form.material.errors %}is-invalid{% endif %}" required>
                        <option value="">Selecione um material</option>
                        {% for material in form.fields.material.queryset %}
                            <option value="{{ material.id }}" {% if form.material.value|stringformat:'s' == material.id|stringformat:'s' %}selected{% endif %}
                                    data-nome="{{ material.nome }}" data-diametro="{{ material.diametro }}">
                                {{ material.nome }} - {{ material.diametro }}
                            </option>
                        {% endfor %}
                    </select>
                </div>

                <div class="col-md-6">
                    <label for="{{ form.tipo.id_for_label }}" class="form-label">Tipo *</label>
                    {{ form.tipo.errors }}
                    <select name="{{ form.tipo.name }}" id="{{ form.tipo.id_for_label }}" class="form-select {% if form.tipo.errors %}is-invalid{% endif %}" required>
                        {% for value, text in form.fields.tipo.choices %}
                            <option value="{{ value }}" {% if form.tipo.value == value %}selected{% endif %}>{{ text }}</option>
                        {% endfor %}
                    </select>
                </div>

                <div class="col-md-6">
                    <label for="{{ form.quantidade.id_for_label }}" class="form-label">Quantidade (kg) *</label>
                    {{ form.quantidade.errors }}
                    <input type="number" step="1" name="{{ form.quantidade.name }}" id="{{ form.quantidade.id_for_label }}" class="form-control {% if form.quantidade.errors %}is-invalid{% endif %}" value="{{ form.quantidade.value|default:'' }}" min="1" required>
                </div>

                <div class="col-md-6">
                    <label for="{{ form.ordem_compra.id_for_label }}" class="form-label">Ordem de Compra</label>
                    {{ form.ordem_compra.errors }}
                    <input type="text" name="{{ form.ordem_compra.name }}" id="{{ form.ordem_compra.id_for_label }}" class="form-control {% if form.ordem_compra.errors %}is-invalid{% endif %}" value="{{ form.ordem_compra.value|default:'' }}">
                </div>

                <div class="col-md-12">
                    <label for="{{ form.observacao.id_for_label }}" class="form-label">Observação</label>
                    {{ form.observacao.errors }}
                    <textarea name="{{ form.observacao.name }}" id="{{ form.observacao.id_for_label }}" class="form-control {% if form.observacao.errors %}is-invalid{% endif %}" rows="3">{{ form.observacao.value|default:'' }}</textarea>
                </div>

                <div class="col-12 mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Salvar
                    </button>
                    <a href="{% url 'movimentacao-material-list' %}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Cancelar
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Ordenar os materiais no select
        const materialSelect = document.getElementById('{{ form.material.id_for_label }}');

        if (materialSelect) {
            // Obter todas as opções (exceto a primeira que é "Selecione um material")
            const options = Array.from(materialSelect.options).slice(1);

            // Ordenar as opções por nome e diâmetro
            options.sort((a, b) => {
                const nomeA = a.getAttribute('data-nome') || '';
                const nomeB = b.getAttribute('data-nome') || '';

                // Se os nomes são diferentes, ordenar por nome
                if (nomeA !== nomeB) {
                    return nomeA.localeCompare(nomeB);
                }

                // Se os nomes são iguais, ordenar por diâmetro numérico
                const diametroA = extractNumericValue(a.getAttribute('data-diametro') || '');
                const diametroB = extractNumericValue(b.getAttribute('data-diametro') || '');

                return diametroA - diametroB;
            });

            // Remover todas as opções exceto a primeira
            while (materialSelect.options.length > 1) {
                materialSelect.remove(1);
            }

            // Adicionar as opções ordenadas de volta ao select
            options.forEach(option => {
                materialSelect.add(option);
            });

            // Restaurar a seleção anterior se houver
            const selectedValue = '{{ form.material.value|default:"" }}';
            if (selectedValue) {
                materialSelect.value = selectedValue;
            }
        }
    });
</script>
{% endblock %}