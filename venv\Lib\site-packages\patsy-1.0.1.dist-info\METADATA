Metadata-Version: 2.1
Name: patsy
Version: 1.0.1
Summary: A Python package for describing statistical models and for building design matrices.
Home-page: https://github.com/pydata/patsy
Author: <PERSON>
Author-email: <EMAIL>
License: 2-clause BSD
Platform: UNKNOWN
Classifier: Development Status :: 6 - Mature
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Science/Research
Classifier: Intended Audience :: Financial and Insurance Industry
Classifier: License :: OSI Approved :: BSD License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Topic :: Scientific/Engineering
Requires-Python: >=3.6
Description-Content-Type: text/markdown
Requires-Dist: numpy >=1.4
Provides-Extra: test
Requires-Dist: pytest ; extra == 'test'
Requires-Dist: pytest-cov ; extra == 'test'
Requires-Dist: scipy ; extra == 'test'

# Patsy

**Notice:** `patsy` is no longer under active development. As of August 2021,
Matthew Wardrop (@matthewwardrop) and Tomás Capretto (@tomicapretto) have taken
on responsibility from Nathaniel Smith (@njsmith) for keeping the lights on, but
no new feature development is planned. The spiritual successor of this project
is [Formulaic](https://github.com/matthewwardrop/formulaic), and we
recommend that users [migrate](https://matthewwardrop.github.io/formulaic/migration/)
when possible. For the time being, until major software packages have successfully
transitioned, we will attempt to keep `patsy` working in its current state with
current releases in the Python ecosystem.

---

Patsy is a Python library for describing statistical models
(especially linear models, or models that have a linear component) and
building design matrices. Patsy brings the convenience of [R](http://www.r-project.org/) "formulas" to Python.

[![PyPI - Version](https://img.shields.io/pypi/v/patsy.svg)](https://pypi.org/project/spec-classes/)
![PyPI - Python Version](https://img.shields.io/pypi/pyversions/patsy.svg)
![https://patsy.readthedocs.io/](https://img.shields.io/badge/docs-read%20now-blue.svg)
![PyPI - Status](https://img.shields.io/pypi/status/patsy.svg)
![https://coveralls.io/r/pydata/patsy?branch=master](https://coveralls.io/repos/pydata/patsy/badge.png?branch=master)
![https://doi.org/10.5281/zenodo.592075](https://zenodo.org/badge/DOI/10.5281/zenodo.592075.svg)

- **Documentation:** <https://patsy.readthedocs.io/>
- **Downloads:** <http://pypi.python.org/pypi/patsy/>
- **Code and issues:** <https://github.com/pydata/patsy>
- **Mailing list:** <<EMAIL>> (<http://groups.google.com/group/pydata>)


## Dependencies

  * Python (3.6+)
  * numpy
  * Optional:
    * pytest/pytest-cov: needed to run tests
    * scipy: needed for spline-related functions like ``bs``

## Installation
  ``pip install patsy`` (or, for traditionalists: ``python setup.py install``)

## License

2-clause BSD, see LICENSE.txt for details.


