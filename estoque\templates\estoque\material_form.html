{% extends 'estoque/base.html' %}

{% block title %}{% if form.instance.pk %}Editar{% else %}Novo{% endif %} Material - Molas <PERSON>s{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>{% if form.instance.pk %}Editar{% else %}Novo{% endif %} Material</h1>
        <a href="{% url 'material-list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Voltar
        </a>
    </div>

    <div class="card shadow">
        <div class="card-body">
            <form method="post" class="row g-3">
                {% csrf_token %}

                <div class="col-md-12">
                    <label for="{{ form.material_padrao.id_for_label }}" class="form-label">Material Padrão *</label>
                    {{ form.material_padrao.errors }}
                    <select name="{{ form.material_padrao.name }}" id="{{ form.material_padrao.id_for_label }}" class="form-select {% if form.material_padrao.errors %}is-invalid{% endif %}" required>
                        <option value="">Selecione um material padrão</option>
                        {% for material_padrao in form.fields.material_padrao.queryset %}
                            <option value="{{ material_padrao.id }}" {% if form.material_padrao.value|stringformat:'s' == material_padrao.id|stringformat:'s' %}selected{% endif %}>
                                {{ material_padrao.nome }} - {{ material_padrao.diametro }}
                            </option>
                        {% endfor %}
                    </select>
                    <div class="form-text">O nome do material será herdado automaticamente do Material Padrão selecionado</div>
                </div>

                <div class="col-md-12">
                    <label for="{{ form.descricao.id_for_label }}" class="form-label">Descrição</label>
                    {{ form.descricao.errors }}
                    <textarea name="{{ form.descricao.name }}" id="{{ form.descricao.id_for_label }}" class="form-control {% if form.descricao.errors %}is-invalid{% endif %}" rows="3">{{ form.descricao.value|default:'' }}</textarea>
                </div>

                <div class="col-md-4">
                    <label for="{{ form.quantidade_estoque.id_for_label }}" class="form-label">Quantidade em Estoque (kg) *</label>
                    {{ form.quantidade_estoque.errors }}
                    {{ form.quantidade_estoque }}
                    <div class="form-text">{{ form.quantidade_estoque.help_text }}</div>
                </div>

                <div class="col-md-4">
                    <label for="{{ form.estoque_minimo.id_for_label }}" class="form-label">Estoque Mínimo *</label>
                    {{ form.estoque_minimo.errors }}
                    {{ form.estoque_minimo }}
                    <div class="form-text">{{ form.estoque_minimo.help_text }}</div>
                </div>

                <div class="col-md-4">
                    <label for="{{ form.fornecedor.id_for_label }}" class="form-label">Fornecedor</label>
                    {{ form.fornecedor.errors }}
                    <input type="text" name="{{ form.fornecedor.name }}" id="{{ form.fornecedor.id_for_label }}" class="form-control {% if form.fornecedor.errors %}is-invalid{% endif %}" value="{{ form.fornecedor.value|default:'' }}">
                </div>

                <div class="col-md-4">
                    <label for="{{ form.data.id_for_label }}" class="form-label">Data</label>
                    {{ form.data.errors }}
                    <input type="date" name="{{ form.data.name }}" id="{{ form.data.id_for_label }}" class="form-control {% if form.data.errors %}is-invalid{% endif %}" value="{{ form.data.value|date:'Y-m-d'|default:'' }}">
                </div>

                <div class="col-md-4">
                    <label for="{{ form.nota_fiscal.id_for_label }}" class="form-label">Nota Fiscal</label>
                    {{ form.nota_fiscal.errors }}
                    <input type="text" name="{{ form.nota_fiscal.name }}" id="{{ form.nota_fiscal.id_for_label }}" class="form-control {% if form.nota_fiscal.errors %}is-invalid{% endif %}" value="{{ form.nota_fiscal.value|default:'' }}">
                </div>

                <div class="col-12 mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Salvar
                    </button>
                    <a href="{% url 'material-list' %}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Cancelar
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
