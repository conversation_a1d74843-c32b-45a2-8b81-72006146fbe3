{% extends 'estoque/base.html' %}

{% block title %}{% if form.instance.pk %}Editar{% else %}Nova{% endif %} Mola - Controle de Estoque{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>{% if form.instance.pk %}Editar{% else %}Nova{% endif %} Mola</h1>
        <a href="{% url 'mola-list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Voltar
        </a>
    </div>

    <div class="card shadow">
        <div class="card-body">
            <form method="post" class="row g-3">
                {% csrf_token %}

                <div class="col-md-4">
                    <label for="{{ form.codigo.id_for_label }}" class="form-label">Código *</label>
                    {{ form.codigo.errors }}
                    <input type="text" name="{{ form.codigo.name }}" id="{{ form.codigo.id_for_label }}" class="form-control {% if form.codigo.errors %}is-invalid{% endif %}" value="{{ form.codigo.value|default:'' }}" required>
                    {% if form.codigo.help_text %}
                        <div class="form-text">{{ form.codigo.help_text }}</div>
                    {% endif %}
                </div>

                <div class="col-md-2">
                    <label for="{{ form.nome_mola.id_for_label }}" class="form-label">Nome da Mola</label>
                    {{ form.nome_mola.errors }}
                    <input type="text" name="{{ form.nome_mola.name }}" id="{{ form.nome_mola.id_for_label }}" class="form-control {% if form.nome_mola.errors %}is-invalid{% endif %}" value="{{ form.nome_mola.value|default:'' }}">
                    <div class="form-text">Digite o número/nome da mola (ex: 01, 02, etc)</div>
                </div>

                <div class="col-md-6">
                    <label for="{{ form.cliente.id_for_label }}" class="form-label">Cliente *</label>
                    {{ form.cliente.errors }}
                    <input type="text" name="{{ form.cliente.name }}" id="{{ form.cliente.id_for_label }}" class="form-control {% if form.cliente.errors %}is-invalid{% endif %}" value="{{ form.cliente.value|default:'' }}" required>
                </div>

                <div class="col-md-12">
                    <label for="{{ form.descricao.id_for_label }}" class="form-label">Descrição</label>
                    {{ form.descricao.errors }}
                    <textarea name="{{ form.descricao.name }}" id="{{ form.descricao.id_for_label }}" class="form-control {% if form.descricao.errors %}is-invalid{% endif %}" rows="3">{{ form.descricao.value|default:'' }}</textarea>
                </div>

                <div class="col-md-6">
                    <label for="{{ form.material_padrao.id_for_label }}" class="form-label">Material Padrão *</label>
                    {{ form.material_padrao.errors }}
                    <select name="{{ form.material_padrao.name }}" id="{{ form.material_padrao.id_for_label }}" class="form-select {% if form.material_padrao.errors %}is-invalid{% endif %}" required>
                        <option value="">---------</option>
                        {% for material_padrao in form.fields.material_padrao.queryset %}
                            <option value="{{ material_padrao.id }}" {% if form.material_padrao.value|stringformat:'s' == material_padrao.id|stringformat:'s' %}selected{% endif %}>
                                {{ material_padrao.nome }} - {{ material_padrao.diametro }}
                            </option>
                        {% endfor %}
                    </select>
                </div>

                <div class="col-md-6">
                    <label for="{{ form.material.id_for_label }}" class="form-label">Material</label>
                    {{ form.material.errors }}
                    <select name="{{ form.material.name }}" id="{{ form.material.id_for_label }}" class="form-select {% if form.material.errors %}is-invalid{% endif %}">
                        <option value="">---------</option>
                        {% for material in form.fields.material.queryset %}
                            <option value="{{ material.id }}" {% if form.material.value|stringformat:'s' == material.id|stringformat:'s' %}selected{% endif %}>
                                {{ material }}
                            </option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Campo de diâmetro oculto que será preenchido automaticamente com o valor do material padrão -->
                <input type="hidden" name="{{ form.diametro.name }}" id="{{ form.diametro.id_for_label }}" value="{{ form.diametro.value|default:'' }}">

                <div class="col-md-3">
                    <label for="{{ form.volumes.id_for_label }}" class="form-label">Volumes</label>
                    {{ form.volumes.errors }}
                    <input type="text" name="{{ form.volumes.name }}" id="{{ form.volumes.id_for_label }}" class="form-control {% if form.volumes.errors %}is-invalid{% endif %}" value="Cálculo automático" readonly disabled>
                    <input type="hidden" name="{{ form.volumes.name }}" value="{{ form.volumes.value|default:'0' }}">
                </div>

                <div class="col-md-3">
                    <label for="{{ form.quantidade_por_volume.id_for_label }}" class="form-label">Quantidade por Volume</label>
                    {{ form.quantidade_por_volume.errors }}
                    <input type="number" name="{{ form.quantidade_por_volume.name }}" id="{{ form.quantidade_por_volume.id_for_label }}" class="form-control {% if form.quantidade_por_volume.errors %}is-invalid{% endif %}" value="{{ form.quantidade_por_volume.value|default:'' }}">
                </div>

                <div class="col-md-4">
                    <label for="{{ form.quantidade_estoque.id_for_label }}" class="form-label">Quantidade em Estoque *</label>
                    {{ form.quantidade_estoque.errors }}
                    <input type="number" name="{{ form.quantidade_estoque.name }}" id="{{ form.quantidade_estoque.id_for_label }}" class="form-control {% if form.quantidade_estoque.errors %}is-invalid{% endif %}" value="{{ form.quantidade_estoque.value|default:'0' }}" required>
                </div>

                <div class="col-md-4">
                    <label for="{{ form.estoque_minimo.id_for_label }}" class="form-label">Estoque Mínimo *</label>
                    {{ form.estoque_minimo.errors }}
                    <input type="number" name="{{ form.estoque_minimo.name }}" id="{{ form.estoque_minimo.id_for_label }}" class="form-control {% if form.estoque_minimo.errors %}is-invalid{% endif %}" value="{{ form.estoque_minimo.value|default:'0' }}" required>
                </div>

                <div class="col-md-4">
                    <label for="{{ form.ordem_fabricacao.id_for_label }}" class="form-label">Ordem de Fabricação</label>
                    {{ form.ordem_fabricacao.errors }}
                    <input type="text" name="{{ form.ordem_fabricacao.name }}" id="{{ form.ordem_fabricacao.id_for_label }}" class="form-control {% if form.ordem_fabricacao.errors %}is-invalid{% endif %}" value="{{ form.ordem_fabricacao.value|default:'' }}">
                </div>

                <div class="col-md-4">
                    <label for="{{ form.data.id_for_label }}" class="form-label">Data</label>
                    {{ form.data.errors }}
                    <input type="date" name="{{ form.data.name }}" id="{{ form.data.id_for_label }}" class="form-control {% if form.data.errors %}is-invalid{% endif %}" value="{{ form.data.value|date:'Y-m-d'|default:'' }}">
                </div>

                <div class="col-md-4">
                    <label for="{{ form.peso_unitario.id_for_label }}" class="form-label">Peso Unitário (g)</label>
                    {{ form.peso_unitario.errors }}
                    <!-- Usar o campo renderizado pelo Django -->
                    {{ form.peso_unitario }}
                    <!-- Campo oculto para armazenar o valor original -->
                    <input type="hidden" id="peso_unitario_original" value="{{ form.instance.peso_unitario|default:'' }}">
                    <div class="form-text">{{ form.peso_unitario.help_text }} (até 4 casas decimais)</div>
                </div>

                <div class="col-12 mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Salvar
                    </button>
                    <a href="{% url 'mola-list' %}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Cancelar
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Elementos do formulário
        const materialPadraoSelect = document.getElementById('{{ form.material_padrao.id_for_label }}');
        const materialSelect = document.getElementById('{{ form.material.id_for_label }}');
        const diametroInput = document.getElementById('{{ form.diametro.id_for_label }}');
        const pesoUnitarioInput = document.getElementById('{{ form.peso_unitario.id_for_label }}');

        // Obter o valor original do campo peso_unitario
        const pesoUnitarioOriginal = document.getElementById('peso_unitario_original').value;

        // Função para formatar valor decimal com vírgula (formato brasileiro)
        function formatarDecimalBR(valor) {
            if (!valor) return '';
            // Substituir ponto por vírgula
            return String(valor).replace('.', ',');
        }

        // Função para garantir que o valor do peso_unitario seja definido corretamente
        function definirPesoUnitario() {
            // Verificar se o campo está vazio
            if (!pesoUnitarioInput.value) {
                // Tentar definir o valor usando diferentes fontes
                if (pesoUnitarioOriginal) {
                    pesoUnitarioInput.value = formatarDecimalBR(pesoUnitarioOriginal);
                } else if ('{{ peso_unitario_valor }}') {
                    pesoUnitarioInput.value = formatarDecimalBR('{{ peso_unitario_valor }}');
                } else if ('{{ form.instance.peso_unitario }}') {
                    pesoUnitarioInput.value = formatarDecimalBR('{{ form.instance.peso_unitario }}');
                } else if ('{{ form.initial.peso_unitario }}') {
                    pesoUnitarioInput.value = formatarDecimalBR('{{ form.initial.peso_unitario }}');
                }
            }
        }

        // Executar a função imediatamente
        definirPesoUnitario();

        // Executar a função novamente após um pequeno atraso para garantir que o valor seja definido
        setTimeout(definirPesoUnitario, 100);

        // Adicionar um evento para garantir que o valor seja mantido
        pesoUnitarioInput.addEventListener('focus', function() {
            // Se o campo estiver vazio quando receber foco, definir o valor
            if (!this.value && pesoUnitarioOriginal) {
                this.value = formatarDecimalBR(pesoUnitarioOriginal);
            }
        });

        // Adicionar evento para garantir que o valor seja enviado corretamente
        document.querySelector('form').addEventListener('submit', function(e) {
            // Garantir que o campo peso_unitario tenha um valor válido
            if (pesoUnitarioInput.value) {
                // Não precisamos converter aqui, pois o backend já fará isso
                // Apenas garantir que o valor não seja perdido
            }
        });

        // Armazenar todas as opções originais do select de materiais
        const todasOpcoesOriginais = Array.from(materialSelect.options);

        // Função para atualizar o campo de material com base no material padrão selecionado
        function atualizarMateriais() {
            // Obter o ID do material padrão selecionado
            const materialPadraoId = materialPadraoSelect.value;

            // Desabilitar o campo de material enquanto carrega
            materialSelect.disabled = true;

            if (materialPadraoId) {
                // Fazer requisição AJAX para obter materiais compatíveis
                fetch(`/api/material/filtrar/?material_padrao=${materialPadraoId}`)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`Erro HTTP: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        // Limpar o select de materiais
                        materialSelect.innerHTML = '';

                        // Adicionar opção vazia
                        const opcaoVazia = document.createElement('option');
                        opcaoVazia.value = '';
                        opcaoVazia.textContent = '---------';
                        materialSelect.appendChild(opcaoVazia);

                        // Preencher o campo de diâmetro com o valor do material padrão
                        if (data.material_padrao && data.material_padrao.diametro) {
                            diametroInput.value = data.material_padrao.diametro;
                        }

                        if (data.materiais && data.materiais.length > 0) {
                            // Adicionar os materiais filtrados
                            data.materiais.forEach(material => {
                                const opcao = document.createElement('option');
                                opcao.value = material.id;
                                opcao.textContent = material.nome;
                                materialSelect.appendChild(opcao);
                            });

                            // Remover mensagem de erro se existir
                            const mensagemErro = document.getElementById('material-erro');
                            if (mensagemErro) {
                                mensagemErro.remove();
                            }
                        } else {
                            // Se não houver materiais para o material padrão selecionado, mostrar mensagem
                            // Verificar se já existe uma mensagem de erro
                            let mensagemErro = document.getElementById('material-erro');
                            if (!mensagemErro) {
                                mensagemErro = document.createElement('div');
                                mensagemErro.id = 'material-erro';
                                mensagemErro.className = 'alert alert-warning mt-2';
                                mensagemErro.innerHTML = 'Não há materiais variantes cadastrados para este Material Padrão.';
                                materialSelect.parentNode.appendChild(mensagemErro);
                            }
                        }

                        // Reativar o campo de material
                        materialSelect.disabled = false;
                    })
                    .catch(error => {
                        console.error('Erro ao buscar materiais:', error);

                        // Em caso de erro, mostrar todos os materiais
                        materialSelect.innerHTML = '';

                        // Adicionar opção vazia
                        const opcaoVazia = document.createElement('option');
                        opcaoVazia.value = '';
                        opcaoVazia.textContent = '---------';
                        materialSelect.appendChild(opcaoVazia);

                        // Adicionar todas as opções originais
                        todasOpcoesOriginais.forEach(opcao => {
                            if (opcao.value) { // Não adicionar a opção vazia novamente
                                materialSelect.appendChild(opcao.cloneNode(true));
                            }
                        });

                        // Reativar o campo de material
                        materialSelect.disabled = false;

                        // Mostrar mensagem de erro
                        const mensagemErro = document.createElement('div');
                        mensagemErro.id = 'material-erro';
                        mensagemErro.className = 'alert alert-danger mt-2';
                        mensagemErro.innerHTML = 'Erro ao buscar materiais. Mostrando todos os materiais disponíveis.';
                        materialSelect.parentNode.appendChild(mensagemErro);
                    });
            } else {
                // Se nenhum material padrão for selecionado, mostrar todos os materiais
                materialSelect.innerHTML = '';

                // Adicionar opção vazia
                const opcaoVazia = document.createElement('option');
                opcaoVazia.value = '';
                opcaoVazia.textContent = '---------';
                materialSelect.appendChild(opcaoVazia);

                // Adicionar todas as opções originais
                todasOpcoesOriginais.forEach(opcao => {
                    if (opcao.value) { // Não adicionar a opção vazia novamente
                        materialSelect.appendChild(opcao.cloneNode(true));
                    }
                });

                // Remover mensagem de erro se existir
                const mensagemErro = document.getElementById('material-erro');
                if (mensagemErro) {
                    mensagemErro.remove();
                }

                // Reativar o campo de material
                materialSelect.disabled = false;
            }
        }

        // Adicionar evento de mudança ao select de material padrão
        materialPadraoSelect.addEventListener('change', atualizarMateriais);

        // Executar a função inicialmente para configurar o estado inicial
        if (materialPadraoSelect.value) {
            atualizarMateriais();
        }
    });
</script>
{% endblock %}

{% endblock %}