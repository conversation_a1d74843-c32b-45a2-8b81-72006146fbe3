"""
Django settings for controle_estoque project.

Este arquivo contém todas as configurações do projeto de Controle de Estoque da Molas Rios.
Foi otimizado para melhor desempenho e estabilidade.
"""

import os
from pathlib import Path
from .env import get_env_variable, get_env_bool, get_env_int, get_env_list, get_secret_key

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = get_secret_key()

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = get_env_bool('DJANGO_DEBUG', False)

# Definir hosts permitidos a partir de variáveis de ambiente
ALLOWED_HOSTS = get_env_list('DJANGO_ALLOWED_HOSTS', ['localhost', '127.0.0.1'])


# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'estoque',
    'crispy_forms',
    'django_filters',
]

CRISPY_TEMPLATE_PACK = 'bootstrap4'

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    # Removido middleware de cache para evitar atrasos nas atualizações
    # 'django.middleware.cache.UpdateCacheMiddleware',
    'django.middleware.common.CommonMiddleware',
    # 'django.middleware.cache.FetchFromCacheMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    # Middleware para lidar com problemas de timezone
    'estoque.middleware.TimezoneMiddleware',
]

ROOT_URLCONF = 'controle_estoque.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates'],
        'APP_DIRS': True,  # Garantir que os templates dos apps sejam encontrados
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                'django.template.context_processors.media',
                'django.template.context_processors.static',
            ],
        },
    },
]

WSGI_APPLICATION = 'controle_estoque.wsgi.application'


# Database
# https://docs.djangoproject.com/en/5.2/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}


# Password validation
# https://docs.djangoproject.com/en/5.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.2/topics/i18n/

LANGUAGE_CODE = 'pt-br'

TIME_ZONE = 'America/Sao_Paulo'

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.2/howto/static-files/

STATIC_URL = 'static/'
STATIC_ROOT = BASE_DIR / 'staticfiles'
STATICFILES_DIRS = [BASE_DIR / 'static']

MEDIA_URL = '/media/'
MEDIA_ROOT = BASE_DIR / 'media'

# Default primary key field type
# https://docs.djangoproject.com/en/5.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'



# Configurações de cache otimizadas para desempenho
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'molas_rios_cache',
        'TIMEOUT': 300,  # 5 minutos
        'OPTIONS': {
            'MAX_ENTRIES': 1000,
            'CULL_FREQUENCY': 3,  # Remover 1/3 das entradas quando o cache estiver cheio
        }
    }
}

# Cache de views para páginas frequentemente acessadas
CACHE_MIDDLEWARE_SECONDS = 300  # 5 minutos
CACHE_MIDDLEWARE_KEY_PREFIX = 'molas_rios'

# Não modificar a configuração de APP_DIRS para garantir que os templates sejam encontrados
# Usar cache de templates de forma mais segura
if not DEBUG:  # Apenas em produção
    TEMPLATES[0]['OPTIONS']['loaders'] = [
        ('django.template.loaders.cached.Loader', [
            'django.template.loaders.filesystem.Loader',
            'django.template.loaders.app_directories.Loader',
        ]),
    ]

# Configurações de sessão otimizadas
SESSION_ENGINE = 'django.contrib.sessions.backends.cached_db'  # Usar cache + banco de dados
SESSION_CACHE_ALIAS = 'default'
SESSION_COOKIE_AGE = 86400  # 24 horas em segundos
SESSION_SAVE_EVERY_REQUEST = False  # Não salvar a sessão em cada requisição

# Configurações de otimização de banco de dados
DATABASE_OPTIONS = {
    'timeout': 30,  # Timeout em segundos
}

# Otimização de consultas ao banco de dados
CONN_MAX_AGE = 300  # Manter conexões abertas por 5 minutos

# Otimização de arquivos estáticos
STATICFILES_STORAGE = 'django.contrib.staticfiles.storage.ManifestStaticFilesStorage'

# Desativar I18N se não estiver usando internacionalização
USE_I18N = False

# Configurações de logging melhoradas
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,  # Não desativar loggers existentes para evitar perda de logs
    'formatters': {
        'verbose': {
            'format': '{asctime} {levelname} {module} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'console': {
            'level': 'INFO',  # Capturar informações importantes
            'class': 'logging.StreamHandler',
            'formatter': 'simple',
        },
        'file': {
            'level': 'WARNING',  # Capturar avisos e erros
            'class': 'logging.FileHandler',
            'filename': BASE_DIR / 'logs/error.log',
            'formatter': 'verbose',
            'delay': True,  # Abrir o arquivo apenas quando necessário
        },
        'debug_file': {
            'level': 'DEBUG',
            'class': 'logging.FileHandler',
            'filename': BASE_DIR / 'logs/debug.log',
            'formatter': 'verbose',
            'delay': True,
        },
    },
    'loggers': {
        'django': {
            'handlers': ['console', 'file'],
            'level': 'INFO',
            'propagate': True,
        },
        'estoque': {
            'handlers': ['console', 'file', 'debug_file'],
            'level': 'DEBUG',
            'propagate': True,
        },
    },
}

# Configurações de segurança adicionais
# Estas configurações são recomendadas para ambientes de produção

# Redirecionar para HTTPS
SECURE_SSL_REDIRECT = get_env_bool('SECURE_SSL_REDIRECT', False)

# Configurações de cookies seguros
SESSION_COOKIE_SECURE = get_env_bool('SESSION_COOKIE_SECURE', False)
CSRF_COOKIE_SECURE = get_env_bool('CSRF_COOKIE_SECURE', False)

# HTTP Strict Transport Security (HSTS)
SECURE_HSTS_SECONDS = get_env_int('SECURE_HSTS_SECONDS', 0)
SECURE_HSTS_INCLUDE_SUBDOMAINS = get_env_bool('SECURE_HSTS_INCLUDE_SUBDOMAINS', False)
SECURE_HSTS_PRELOAD = get_env_bool('SECURE_HSTS_PRELOAD', False)

# Proteção contra MIME type sniffing
SECURE_CONTENT_TYPE_NOSNIFF = get_env_bool('SECURE_CONTENT_TYPE_NOSNIFF', True)

# Proteção contra XSS
SECURE_BROWSER_XSS_FILTER = get_env_bool('SECURE_BROWSER_XSS_FILTER', True)

# Proteção contra clickjacking
X_FRAME_OPTIONS = get_env_variable('X_FRAME_OPTIONS', 'DENY')

# Configurações de senha
PASSWORD_HASHERS = [
    'django.contrib.auth.hashers.Argon2PasswordHasher',
    'django.contrib.auth.hashers.PBKDF2PasswordHasher',
    'django.contrib.auth.hashers.PBKDF2SHA1PasswordHasher',
    'django.contrib.auth.hashers.BCryptSHA256PasswordHasher',
]



