{% extends 'estoque/base.html' %}

{% block title %}Movimentações de Estoque - Molas Rios{% endblock %}

{% block extra_css %}
<style>
    .observacao-cell {
        max-width: 300px;
        white-space: normal;
        word-wrap: break-word;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Movimentações de Estoque</h1>
        <div>
            <a href="{% url 'movimentacao-estoque-create' %}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Nova Movimentação
            </a>
            <a href="{% url 'movimentacao-multipla' %}" class="btn btn-success">
                <i class="fas fa-layer-group"></i> Movimentação Múltipla
            </a>
            <a href="{% url 'limpar-historico-estoque' %}" class="btn btn-warning">
                <i class="fas fa-broom"></i> Limpar Histórico
            </a>
            <a href="{% url 'listar-backups-estoque' %}" class="btn btn-info">
                <i class="fas fa-history"></i> Backups
            </a>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold">Filtros</h6>
        </div>
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    <label for="id_mola" class="form-label">Mola</label>
                    <select name="mola" id="id_mola" class="form-select">
                        <option value="">---------</option>
                        {% for mola in filter.form.fields.mola.queryset %}
                            <option value="{{ mola.id }}" {% if filter.form.mola.value|stringformat:'s' == mola.id|stringformat:'s' %}selected{% endif %}>
                                {{ mola }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="id_tipo" class="form-label">Tipo</label>
                    <select name="tipo" id="id_tipo" class="form-select">
                        <option value="">---------</option>
                        {% for value, text in filter.form.fields.tipo.choices %}
                            <option value="{{ value }}" {% if filter.form.tipo.value == value %}selected{% endif %}>{{ text }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="id_data_inicio" class="form-label">Data Inicial</label>
                    <input type="date" name="data_inicio" id="id_data_inicio" class="form-control" value="{{ filter.form.data_inicio.value|date:'Y-m-d'|default:'' }}">
                </div>
                <div class="col-md-3">
                    <label for="id_data_fim" class="form-label">Data Final</label>
                    <input type="date" name="data_fim" id="id_data_fim" class="form-control" value="{{ filter.form.data_fim.value|date:'Y-m-d'|default:'' }}">
                </div>
                <div class="col-12">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Filtrar
                    </button>
                    <a href="{% url 'movimentacao-estoque-list' %}" class="btn btn-secondary">
                        <i class="fas fa-undo"></i> Limpar Filtros
                    </a>
                </div>
            </form>
        </div>
    </div>

    <div class="card shadow">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold">Histórico de Movimentações</h6>
            <div>
                <small class="text-muted mr-3">Última atualização: {{ timestamp|date:"d/m/Y H:i:s" }}</small>
                <a href="{% url 'movimentacao-estoque-list' %}{% if request.GET %}?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}" class="btn btn-sm btn-info">
                    <i class="fas fa-sync-alt"></i> Atualizar
                </a>
            </div>
        </div>
        <div class="card-body">
            {% if movimentacoes %}
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>Data</th>
                                <th>Mola</th>
                                <th>Cliente</th>
                                <th>Tipo</th>
                                <th>Quantidade</th>
                                <th>Ordem de Venda</th>
                                <th>Observação</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for mov in movimentacoes %}
                                <tr>
                                    <td>{{ mov.data|date:"d/m/Y H:i" }}</td>
                                    <td>
                                        <a href="{% url 'mola-detail' mov.mola.id %}">{{ mov.mola.codigo }}</a>
                                    </td>
                                    <td>{{ mov.mola.cliente }}</td>
                                    <td>
                                        {% if mov.tipo == 'E' %}
                                            <span class="badge bg-success">Entrada</span>
                                        {% else %}
                                            <span class="badge bg-danger">Saída</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ mov.quantidade }}</td>
                                    <td>{{ mov.ordem_venda|default:"--" }}</td>
                                    <td class="observacao-cell">{{ mov.observacao|default:"--" }}</td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Paginação -->
                {% if is_paginated %}
                    <nav aria-label="Paginação">
                        <ul class="pagination justify-content-center mt-4">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Primeira">
                                        <span aria-hidden="true">&laquo;&laquo;</span>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Anterior">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                            {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Primeira">
                                        <span aria-hidden="true">&laquo;&laquo;</span>
                                    </a>
                                </li>
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Anterior">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                            {% endif %}

                            {% for num in page_obj.paginator.page_range %}
                                {% if page_obj.number == num %}
                                    <li class="page-item active">
                                        <a class="page-link" href="#">{{ num }}</a>
                                    </li>
                                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ num }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">{{ num }}</a>
                                    </li>
                                {% endif %}
                            {% endfor %}

                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Próxima">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Última">
                                        <span aria-hidden="true">&raquo;&raquo;</span>
                                    </a>
                                </li>
                            {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Próxima">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Última">
                                        <span aria-hidden="true">&raquo;&raquo;</span>
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                {% endif %}
            {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> Nenhuma movimentação encontrada.
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
