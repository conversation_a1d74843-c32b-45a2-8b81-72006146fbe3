# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON>, 2019,2023
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON><PERSON> <<EMAIL>>, 2012
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2023-12-04 20:19+0000\n"
"Last-Translator: <PERSON>, 2019,2023\n"
"Language-Team: Serbian (http://app.transifex.com/django/django/language/"
"sr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: sr\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && "
"n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

msgid "Administrative Documentation"
msgstr "Административна документација"

msgid "Home"
msgstr "Почетна"

msgid "Documentation"
msgstr "Документација"

msgid "Bookmarklets"
msgstr "Букмарклети"

msgid "Documentation bookmarklets"
msgstr "Букмарклети документације"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"Да бисте инсталирали обележиваче, превуците везу на траку са алаткама за "
"обележиваче или кликните десним тастером миша на везу и додајте је у "
"обележиваче. Сада можете да изаберете боокмарклет са било које странице на "
"сајту."

msgid "Documentation for this page"
msgstr "Документација за ову страницу"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr ""
"Води од било које странице до документације погледа који је генерисао ту "
"страницу."

msgid "Tags"
msgstr "Тагови"

msgid "List of all the template tags and their functions."
msgstr "Листа свих ознака шаблона и њихових функција."

msgid "Filters"
msgstr "Филтери"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""
"Филтери су радње које се могу применити на променљиве у шаблону да би се "
"променио излаз."

msgid "Models"
msgstr "Модели"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"Модели су описи свих објеката у систему и њихових повезаних поља. Сваки "
"модел има листу поља којима се може приступити као променљиве шаблона"

msgid "Views"
msgstr "Погледи"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"Сваку страницу на јавном сајту генерише поглед. Поглед дефинише који шаблон "
"се користи за генерисање странице и који објекти су доступни том шаблону."

msgid "Tools for your browser to quickly access admin functionality."
msgstr "Алатке за ваш претраживач за брзи приступ функцијама администратора."

msgid "Please install docutils"
msgstr "Молимо инсталирајте docutils"

#, python-format
msgid ""
"The admin documentation system requires Python’s <a "
"href=\"%(link)s\">docutils</a> library."
msgstr ""
"Систем административне документације захтева Пајтон <a "
"href=\"%(link)s\">docutils</a> библиотеку."

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""
"Замолите своје администраторе да инсталирају <a href=\"%(link)s\">docutils</"
"a>."

#, python-format
msgid "Model: %(name)s"
msgstr "Модел: %(name)s"

msgid "Fields"
msgstr "Поља"

msgid "Field"
msgstr "Поље"

msgid "Type"
msgstr "Тип"

msgid "Description"
msgstr "Опис"

msgid "Methods with arguments"
msgstr "Метода са аргументима"

msgid "Method"
msgstr "Метод"

msgid "Arguments"
msgstr "Аргументи"

msgid "Back to Model documentation"
msgstr "Назад на документацију о Моделима"

msgid "Model documentation"
msgstr "Документација о Моделима"

msgid "Model groups"
msgstr "Групе модела"

msgid "Templates"
msgstr "Шаблони"

#, python-format
msgid "Template: %(name)s"
msgstr "Шаблон: %(name)s"

#, python-format
msgid "Template: <q>%(name)s</q>"
msgstr "Шаблон: <q>%(name)s</q>"

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template <q>%(name)s</q>:"
msgstr "Путања за тражење шаблона <q>%(name)s</q>:"

msgid "(does not exist)"
msgstr "(не постоји)"

msgid "Back to Documentation"
msgstr "Назада на документацију"

msgid "Template filters"
msgstr "Филтери шаблона"

msgid "Template filter documentation"
msgstr "Документација филтера шаблона"

msgid "Built-in filters"
msgstr "Уграђени филтери"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""
"Да бисте користили ове филтере, ставите <code>%(code)s</code> у свој шаблон "
"пре употребе филтера."

msgid "Template tags"
msgstr "Ознаке шаблона"

msgid "Template tag documentation"
msgstr "Документација ознаке шаблона"

msgid "Built-in tags"
msgstr "Уграђене ознаке"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""
"Да бисте користили ове ознаке, ставите <code>%(code)s</code> у свој шаблон "
"пре употребе ознаке."

#, python-format
msgid "View: %(name)s"
msgstr "Поглед: %(name)s"

msgid "Context:"
msgstr "Контекст:"

msgid "Templates:"
msgstr "Шаблони:"

msgid "Back to View documentation"
msgstr "Назад на документацију о погледима"

msgid "View documentation"
msgstr "Документација о погледима"

msgid "Jump to namespace"
msgstr "Скочи на именски простор"

msgid "Empty namespace"
msgstr "Празан именски простор"

#, python-format
msgid "Views by namespace %(name)s"
msgstr "Погледи по именском простору %(name)s"

msgid "Views by empty namespace"
msgstr "Погледи по празном именском простору"

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""
"\n"
"    Функција погледа: <code>%(full_name)s</code>. Име: <code>%(url_name)s</"
"code>.\n"

msgid "tag:"
msgstr "таг:"

msgid "filter:"
msgstr "филтер:"

msgid "view:"
msgstr "поглед:"

#, python-format
msgid "App %(app_label)r not found"
msgstr "Апликација %(app_label)r није пронађена"

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "Модел %(model_name)r није пронађен у апликацији %(app_label)r"

msgid "model:"
msgstr "модел:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "повезани објекти класе `%(app_label)s.%(data_type)s`"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "класе `%(app_label)s.%(object_name)s`"

#, python-format
msgid "all %s"
msgstr "сви повезани објекти %s"

#, python-format
msgid "number of %s"
msgstr "број повезаних објеката %s"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "%s не изгледа као „urlpattern“ објекат"
