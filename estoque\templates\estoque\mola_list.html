{% extends 'estoque/base.html' %}
{% load estoque_extras %}

{% block title %}Molas - Controle de Estoque{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><PERSON><PERSON></h1>
        <a href="{% url 'mola-create' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Nova Mola
        </a>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold">Filtros</h6>
        </div>
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    <label for="id_codigo" class="form-label">Código</label>
                    <input type="text" name="codigo" id="id_codigo" class="form-control" value="{{ filter.form.codigo.value|default:'' }}">
                </div>
                <div class="col-md-3">
                    <label for="id_cliente" class="form-label">Cliente</label>
                    <input type="text" name="cliente" id="id_cliente" class="form-control" value="{{ filter.form.cliente.value|default:'' }}">
                </div>
                <div class="col-md-3">
                    <label for="id_material" class="form-label">Material</label>
                    <select name="material" id="id_material" class="form-select">
                        <option value="">---------</option>
                        {% for material in filter.form.fields.material.queryset %}
                            <option value="{{ material.id }}" {% if filter.form.material.value|stringformat:'s' == material.id|stringformat:'s' %}selected{% endif %}>
                                {{ material }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="id_estoque_baixo" class="form-label">Estoque Baixo</label>
                    <div class="form-check mt-2">
                        <input type="checkbox" name="estoque_baixo" id="id_estoque_baixo" class="form-check-input" {% if filter.form.estoque_baixo.value %}checked{% endif %}>
                        <label class="form-check-label" for="id_estoque_baixo">Mostrar apenas com estoque baixo</label>
                    </div>
                </div>
                <div class="col-12">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Filtrar
                    </button>
                    <a href="{% url 'mola-list' %}" class="btn btn-secondary">
                        <i class="fas fa-undo"></i> Limpar Filtros
                    </a>
                </div>
            </form>
        </div>
    </div>

    <div class="card shadow">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold">Lista de Molas</h6>
            <div>
                <small class="text-muted mr-3">Última atualização: {{ timestamp|date:"d/m/Y H:i:s" }}</small>
                <a href="{% url 'mola-list' %}{% if request.GET %}?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}" class="btn btn-sm btn-info">
                    <i class="fas fa-sync-alt"></i> Atualizar
                </a>
            </div>
        </div>
        <div class="card-body">
            {% if molas %}
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>Cód</th>
                                <th>Mola</th>
                                <th>Cliente</th>
                                <th>Material</th>
                                <th>Diâmetro</th>
                                <th>Estoque</th>
                                <th>Vol</th>
                                <th>Mínimo</th>
                                <th>Status</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for mola in molas %}
                                <tr>
                                    <td>{{ mola.codigo }}</td>
                                    <td>{{ mola.nome_mola|default:"--" }}</td>
                                    <td>{{ mola.cliente }}</td>
                                    <td>
                                        {% if mola.material_padrao %}
                                            {{ mola.material_padrao.nome }}
                                        {% elif mola.material %}
                                            {{ mola.material.nome }}
                                        {% else %}
                                            <span class="text-muted">Não informado</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if mola.diametro %}
                                            {{ mola.diametro }}
                                        {% elif mola.material_padrao %}
                                            {{ mola.material_padrao.diametro }}
                                        {% elif mola.material %}
                                            {{ mola.material.diametro }}
                                        {% else %}
                                            <span class="text-muted">--</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ mola.quantidade_estoque }}</td>
                                    <td>{% if mola.quantidade_por_volume %}{{ mola.quantidade_estoque|intdiv:mola.quantidade_por_volume }}{% else %}--{% endif %}</td>
                                    <td>{{ mola.estoque_minimo }}</td>
                                    <td>
                                        {% if mola.quantidade_estoque <= mola.estoque_minimo %}
                                            <span class="badge bg-danger">Estoque Baixo</span>
                                        {% else %}
                                            <span class="badge bg-success">OK</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{% url 'mola-detail' mola.id %}" class="btn btn-sm btn-info" data-bs-toggle="tooltip" data-bs-placement="top" title="Informações da Mola">
                                            <i class="fas fa-info-circle"></i>
                                        </a>
                                        <a href="{% url 'mola-update' mola.id %}" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" data-bs-placement="top" title="Editar Mola">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{% url 'mola-delete' mola.id %}" class="btn btn-sm btn-danger" data-bs-toggle="tooltip" data-bs-placement="top" title="Excluir Mola">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                        <a href="{% url 'movimentacao-estoque-create' %}?mola={{ mola.id }}&tipo=E" class="btn btn-sm btn-success" data-bs-toggle="tooltip" data-bs-placement="top" title="Adicionar ao Estoque">
                                            <i class="fas fa-plus"></i>
                                        </a>
                                        <a href="{% url 'movimentacao-estoque-create' %}?mola={{ mola.id }}&tipo=S" class="btn btn-sm btn-danger" data-bs-toggle="tooltip" data-bs-placement="top" title="Remover do Estoque">
                                            <i class="fas fa-minus"></i>
                                        </a>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Paginação -->
                {% if is_paginated %}
                    <nav aria-label="Paginação">
                        <ul class="pagination justify-content-center mt-4">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Primeira">
                                        <span aria-hidden="true">&laquo;&laquo;</span>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Anterior">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                            {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Primeira">
                                        <span aria-hidden="true">&laquo;&laquo;</span>
                                    </a>
                                </li>
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Anterior">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                            {% endif %}

                            {% for num in page_obj.paginator.page_range %}
                                {% if page_obj.number == num %}
                                    <li class="page-item active">
                                        <a class="page-link" href="#">{{ num }}</a>
                                    </li>
                                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ num }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">{{ num }}</a>
                                    </li>
                                {% endif %}
                            {% endfor %}

                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Próxima">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Última">
                                        <span aria-hidden="true">&raquo;&raquo;</span>
                                    </a>
                                </li>
                            {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Próxima">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Última">
                                        <span aria-hidden="true">&raquo;&raquo;</span>
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                {% endif %}
            {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> Nenhuma mola encontrada.
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
