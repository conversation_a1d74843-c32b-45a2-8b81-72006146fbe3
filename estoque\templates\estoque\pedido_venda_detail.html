{% extends 'estoque/base.html' %}

{% block title %}Pedido {{ pedido.numero_pedido }} - <PERSON><PERSON>{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Pedido #{{ pedido.numero_pedido }}</h1>
        <div>
            <a href="{% url 'pedido-venda-list' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Voltar
            </a>
            <a href="{% url 'pedido-venda-update' pedido.id %}" class="btn btn-warning">
                <i class="fas fa-edit"></i> Editar
            </a>
            <a href="{% url 'pedido-venda-add-item' pedido.id %}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Adicionar Item
            </a>
            {% if pedido.status == 'P' %}
                <a href="{% url 'pedido-venda-processar' pedido.id %}" class="btn btn-success">
                    <i class="fas fa-check"></i> Processar Pedido
                </a>
            {% endif %}
            {% if pedido.status != 'C' %}
                <a href="{% url 'pedido-venda-cancelar' pedido.id %}" class="btn btn-danger">
                    <i class="fas fa-ban"></i> Cancelar
                </a>
            {% endif %}
            <a href="{% url 'pedido-venda-delete' pedido.id %}" class="btn btn-dark">
                <i class="fas fa-trash"></i> Excluir
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Informações do Pedido -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold">Informações do Pedido</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <tr>
                                <th style="width: 30%">Número do Pedido:</th>
                                <td>{{ pedido.numero_pedido }}</td>
                            </tr>
                            <tr>
                                <th>Cliente:</th>
                                <td>{{ pedido.cliente }}</td>
                            </tr>
                            <tr>
                                <th>Data do Pedido:</th>
                                <td>{{ pedido.data_pedido|date:"d/m/Y" }}</td>
                            </tr>
                            <tr>
                                <th>Status:</th>
                                <td>
                                    {% if pedido.status == 'P' %}
                                        <span class="badge bg-warning">Pendente</span>
                                    {% elif pedido.status == 'A' %}
                                        <span class="badge bg-success">Aprovado</span>
                                    {% elif pedido.status == 'C' %}
                                        <span class="badge bg-danger">Cancelado</span>
                                    {% elif pedido.status == 'F' %}
                                        <span class="badge bg-info">Finalizado</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <th>Observação:</th>
                                <td>{{ pedido.observacao|default:"--" }}</td>
                            </tr>
                            <tr>
                                <th>Data de Cadastro:</th>
                                <td>{{ pedido.data_cadastro|date:"d/m/Y H:i" }}</td>
                            </tr>
                            <tr>
                                <th>Última Atualização:</th>
                                <td>{{ pedido.data_atualizacao|date:"d/m/Y H:i" }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>


        </div>

        <!-- Itens do Pedido -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold">Itens do Pedido</h6>
                    <a href="{% url 'pedido-venda-add-item' pedido.id %}" class="btn btn-sm btn-primary">
                        <i class="fas fa-plus"></i> Adicionar Item
                    </a>
                </div>
                <div class="card-body">
                    {% if itens %}
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Mola</th>
                                        <th>Cliente</th>
                                        <th>Quantidade</th>
                                        <th>Status</th>
                                        <th>Estoque Disponível</th>
                                        <th>Ações</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for item in itens %}
                                        <tr>
                                            <td>
                                                <a href="{% url 'mola-detail' item.mola.id %}">{{ item.mola.codigo }}</a>
                                            </td>
                                            <td>{{ item.mola.cliente }}</td>
                                            <td>{{ item.quantidade }}</td>
                                            <td>
                                                {% if item.atendido %}
                                                    <span class="badge bg-success">Atendido</span>
                                                {% else %}
                                                    <span class="badge bg-warning">Pendente</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if item.mola.quantidade_estoque >= item.quantidade %}
                                                    <span class="text-success">{{ item.mola.quantidade_estoque }}</span>
                                                {% else %}
                                                    <span class="text-danger">{{ item.mola.quantidade_estoque }}</span>
                                                    <span class="badge bg-danger">Insuficiente</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <a href="{% url 'item-pedido-delete' item.id %}" class="btn btn-sm btn-danger" title="Remover item">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-info alert-dismissible fade show">
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fechar"></button>
                            <i class="fas fa-info-circle"></i> Nenhum item adicionado ao pedido.
                            <a href="{% url 'pedido-venda-add-item' pedido.id %}" class="btn btn-sm btn-primary mt-2">
                                <i class="fas fa-plus"></i> Adicionar Item
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
