{% extends 'estoque/base.html' %}

{% block title %}{{ mola.codigo }} - Detalhes <PERSON> Mo<PERSON>{% endblock %}

{% block extra_css %}
<style>
    .observacao-cell {
        max-width: 300px;
        white-space: normal;
        word-wrap: break-word;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>{{ mola.codigo }}</h1>
        <div>
            <a href="{% url 'mola-list' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Voltar
            </a>
            <a href="{% url 'mola-update' mola.id %}" class="btn btn-warning">
                <i class="fas fa-edit"></i> Editar
            </a>
            <a href="{% url 'movimentacao-estoque-create' %}?mola={{ mola.id }}&tipo=E" class="btn btn-success">
                <i class="fas fa-plus"></i> Entrada
            </a>
            <a href="{% url 'movimentacao-estoque-create' %}?mola={{ mola.id }}&tipo=S" class="btn btn-danger">
                <i class="fas fa-minus"></i> Saída
            </a>
            <a href="{% url 'movimentacao-multipla' %}" class="btn btn-info">
                <i class="fas fa-layer-group"></i> Movimentação Múltipla
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Informações da Mola -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold">Informações da Mola</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <tr>
                                <th style="width: 30%">Código:</th>
                                <td>{{ mola.codigo }}</td>
                            </tr>
                            <tr>
                                <th>Nome da Mola:</th>
                                <td>{{ mola.nome_mola|default:"--" }}</td>
                            </tr>
                            <tr>
                                <th>Cliente:</th>
                                <td>{{ mola.cliente }}</td>
                            </tr>
                            <tr>
                                <th>Descrição:</th>
                                <td>{{ mola.descricao|default:"--" }}</td>
                            </tr>
                            <tr>
                                <th>Material Padrão:</th>
                                <td>
                                    {% if mola.material_padrao %}
                                        {{ mola.material_padrao.nome }} - {{ mola.material_padrao.diametro }}
                                    {% else %}
                                        <span class="text-muted">Não informado</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <th>Material Específico:</th>
                                <td>
                                    {% if mola.material %}
                                        {{ mola.material.nome }} - {{ mola.material.diametro }}
                                    {% else %}
                                        <span class="text-muted">Não informado</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <th>Diâmetro:</th>
                                <td>{{ mola.diametro|default:"--" }}</td>
                            </tr>
                            <tr>
                                <th>Volumes:</th>
                                <td>{{ mola.volumes|default:"--" }}</td>
                            </tr>
                            <tr>
                                <th>Quantidade por Volume:</th>
                                <td>{{ mola.quantidade_por_volume|default:"--" }}</td>
                            </tr>
                            <tr>
                                <th>Quantidade em Estoque:</th>
                                <td>
                                    {% if mola.quantidade_estoque <= mola.estoque_minimo %}
                                        <span class="text-danger fw-bold">{{ mola.quantidade_estoque }}</span>
                                        <span class="badge bg-danger">Estoque Baixo</span>
                                    {% else %}
                                        <span class="text-success fw-bold">{{ mola.quantidade_estoque }}</span>
                                        <span class="badge bg-success">OK</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <th>Estoque Mínimo:</th>
                                <td>{{ mola.estoque_minimo }}</td>
                            </tr>
                            <tr>
                                <th>Ordem de Fabricação:</th>
                                <td>{{ mola.ordem_fabricacao|default:"--" }}</td>
                            </tr>
                            <tr>
                                <th>Data:</th>
                                <td>{{ mola.data|date:"d/m/Y"|default:"--" }}</td>
                            </tr>
                            <tr>
                                <th>Peso Unitário:</th>
                                <td>
                                    {% if mola.peso_unitario %}
                                        {{ mola.peso_unitario|floatformat:4 }} g
                                    {% else %}
                                        <span class="text-muted">Não informado</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% if consumo_total %}
                            <tr>
                                <th>Consumo Total de Material:</th>
                                <td>{{ consumo_total|floatformat:2 }} kg</td>
                            </tr>
                            {% endif %}
                            <tr>
                                <th>Data de Cadastro:</th>
                                <td>{{ mola.data_cadastro|date:"d/m/Y H:i" }}</td>
                            </tr>
                            <tr>
                                <th>Última Atualização:</th>
                                <td>{{ mola.data_atualizacao|date:"d/m/Y H:i" }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>

            {% if necessidade_material %}
                <div class="card shadow mb-4">
                    <div class="card-header py-3 {% if not necessidade_material.suficiente %}bg-danger text-white{% else %}bg-success text-white{% endif %}">
                        <h6 class="m-0 font-weight-bold">Necessidade de Material</h6>
                    </div>
                    <div class="card-body">
                        <p>Para produzir a quantidade mínima de estoque, você precisa de:</p>
                        <div class="table-responsive">
                            <table class="table">
                                <tr>
                                    <th>Material Necessário:</th>
                                    <td>{{ necessidade_material.necessario|floatformat:2 }} kg</td>
                                </tr>
                                <tr>
                                    <th>Material Disponível:</th>
                                    <td>{{ necessidade_material.disponivel|floatformat:2 }} kg</td>
                                </tr>
                                {% if not necessidade_material.suficiente %}
                                    <tr>
                                        <th>Material Faltante:</th>
                                        <td class="text-danger fw-bold">{{ necessidade_material.faltante|floatformat:2 }} kg</td>
                                    </tr>
                                {% endif %}
                            </table>
                        </div>

                        {% if not necessidade_material.suficiente %}
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle"></i> Não há material suficiente para produção.
                                {% if mola.material %}
                                <a href="{% url 'movimentacao-material-create' %}?material={{ mola.material.id }}&tipo=E" class="btn btn-sm btn-success mt-2">
                                    <i class="fas fa-plus"></i> Adicionar Material
                                </a>
                                {% else %}
                                <a href="{% url 'mola-update' mola.id %}" class="btn btn-sm btn-warning mt-2">
                                    <i class="fas fa-edit"></i> Definir Material
                                </a>
                                {% endif %}
                            </div>
                        {% else %}
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle"></i> Há material suficiente para produção.
                            </div>
                        {% endif %}
                    </div>
                </div>
            {% endif %}
        </div>

        <!-- Histórico de Movimentações -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold">Histórico de Movimentações</h6>
                    <a href="{% url 'movimentacao-estoque-list' %}?mola={{ mola.id }}" class="btn btn-sm btn-primary">
                        Ver Todas
                    </a>
                </div>
                <div class="card-body">
                    {% if movimentacoes %}
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Data</th>
                                        <th>Tipo</th>
                                        <th>Quantidade</th>
                                        <th>Ordem de Venda</th>
                                        <th>Observação</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for mov in movimentacoes %}
                                        <tr>
                                            <td>{{ mov.data|date:"d/m/Y H:i" }}</td>
                                            <td>
                                                {% if mov.tipo == 'E' %}
                                                    <span class="badge bg-success">Entrada</span>
                                                {% else %}
                                                    <span class="badge bg-danger">Saída</span>
                                                {% endif %}
                                            </td>
                                            <td>{{ mov.quantidade }}</td>
                                            <td>{{ mov.ordem_venda|default:"--" }}</td>
                                            <td class="observacao-cell">{{ mov.observacao|default:"--" }}</td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> Nenhuma movimentação registrada.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
