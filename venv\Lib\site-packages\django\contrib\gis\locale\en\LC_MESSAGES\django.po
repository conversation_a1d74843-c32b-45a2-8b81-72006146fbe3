# This file is distributed under the same license as the Django package.
#
msgid ""
msgstr ""
"Project-Id-Version: Django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-09-18 11:41-0300\n"
"PO-Revision-Date: 2010-05-13 15:35+0200\n"
"Last-Translator: Django team\n"
"Language-Team: English <<EMAIL>>\n"
"Language: en\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: contrib/gis/apps.py:9
msgid "GIS"
msgstr ""

#: contrib/gis/db/models/fields.py:81
msgid "The base GIS field."
msgstr ""

#: contrib/gis/db/models/fields.py:233
msgid ""
"The base Geometry field — maps to the OpenGIS Specification Geometry type."
msgstr ""

#: contrib/gis/db/models/fields.py:332
msgid "Point"
msgstr ""

#: contrib/gis/db/models/fields.py:339
msgid "Line string"
msgstr ""

#: contrib/gis/db/models/fields.py:346
msgid "Polygon"
msgstr ""

#: contrib/gis/db/models/fields.py:353
msgid "Multi-point"
msgstr ""

#: contrib/gis/db/models/fields.py:360
msgid "Multi-line string"
msgstr ""

#: contrib/gis/db/models/fields.py:367
msgid "Multi polygon"
msgstr ""

#: contrib/gis/db/models/fields.py:374
msgid "Geometry collection"
msgstr ""

#: contrib/gis/db/models/fields.py:380
msgid "Extent Aggregate Field"
msgstr ""

#: contrib/gis/db/models/fields.py:395
msgid "Raster Field"
msgstr ""

#: contrib/gis/forms/fields.py:21
msgid "No geometry value provided."
msgstr ""

#: contrib/gis/forms/fields.py:22
msgid "Invalid geometry value."
msgstr ""

#: contrib/gis/forms/fields.py:23
msgid "Invalid geometry type."
msgstr ""

#: contrib/gis/forms/fields.py:25
msgid ""
"An error occurred when transforming the geometry to the SRID of the geometry "
"form field."
msgstr ""

#: contrib/gis/templates/gis/openlayers.html:6
msgid "Delete all Features"
msgstr ""

#: contrib/gis/templates/gis/openlayers.html:7
msgid "Debugging window (serialized value)"
msgstr ""

#: contrib/gis/views.py:8
msgid "No feeds are registered."
msgstr ""

#: contrib/gis/views.py:14
#, python-format
msgid "Slug %r isn’t registered."
msgstr ""
