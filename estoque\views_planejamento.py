from django.shortcuts import render, redirect, get_object_or_404
from django.contrib import messages
from django.utils import timezone
from django.db.models import Sum, F
from django.http import JsonResponse
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from decimal import Decimal

from .models import PlanejamentoProducao, ItemPlanejamento, Mola, Material, MovimentacaoEstoque, MovimentacaoMaterial, PrevisaoDemanda, PedidoVenda
from .forms import PlanejamentoProducaoForm, ItemPlanejamentoForm, PlanejamentoAutomaticoForm, RegistroProducaoForm
from .utils import ordenar_materiais, extrair_valor_numerico_diametro

import json
from datetime import timedelta


class PlanejamentoProducaoListView(ListView):
    model = PlanejamentoProducao
    template_name = 'estoque/planejamento/planejamento_list.html'
    context_object_name = 'planejamentos'
    paginate_by = 10

    def get_queryset(self):
        queryset = super().get_queryset()

        # Filtros
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)

        prioridade = self.request.GET.get('prioridade')
        if prioridade:
            queryset = queryset.filter(prioridade=prioridade)

        # Verificar atrasos
        for plano in queryset:
            plano.verificar_atraso()

            # Adicionar a mola associada ao planejamento
            item = plano.itens.first()
            if item:
                plano.item_mola = item.mola
            else:
                plano.item_mola = None

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Adicionar contadores por status
        context['pendentes'] = PlanejamentoProducao.objects.filter(status='P').count()
        context['em_producao'] = PlanejamentoProducao.objects.filter(status='E').count()
        context['concluidos'] = PlanejamentoProducao.objects.filter(status='C').count()
        context['atrasados'] = PlanejamentoProducao.objects.filter(status='A').count()
        context['cancelados'] = PlanejamentoProducao.objects.filter(status='X').count()

        # Filtros ativos
        context['status_filtro'] = self.request.GET.get('status', '')
        context['prioridade_filtro'] = self.request.GET.get('prioridade', '')

        return context


class PlanejamentoProducaoDetailView(DetailView):
    model = PlanejamentoProducao
    template_name = 'estoque/planejamento/planejamento_detail.html'
    context_object_name = 'planejamento'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Calcular progresso
        context['progresso'] = self.object.calcular_progresso()

        # Calcular necessidade de materiais
        context['necessidades'] = self.object.calcular_necessidade_materiais()

        # Formulário para registrar produção
        context['form_producao'] = RegistroProducaoForm()

        return context


class PlanejamentoProducaoCreateView(CreateView):
    model = PlanejamentoProducao
    form_class = PlanejamentoProducaoForm
    template_name = 'estoque/planejamento/planejamento_form.html'
    success_url = reverse_lazy('planejamento-list')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Adicionar todos os materiais ativos ao contexto
        materiais_ativos = Material.objects.filter(ativo=True)
        context['todos_materiais'] = materiais_ativos

        # Registrar informações para depuração
        print(f"Total de materiais ativos: {materiais_ativos.count()}")

        # Se não há materiais ativos, incluir todos os materiais como fallback
        if materiais_ativos.count() == 0:
            print("ALERTA: Nenhum material ativo encontrado!")

            # Verificar se existem materiais no sistema
            total_materiais = Material.objects.all().count()
            print(f"Total de materiais no sistema: {total_materiais}")

            if total_materiais > 0:
                print("Incluindo todos os materiais (mesmo inativos) como fallback")
                context['todos_materiais'] = Material.objects.all()

                # Listar os materiais para depuração
                for material in context['todos_materiais']:
                    print(f"Material: ID={material.id}, Nome={material}, Ativo={material.ativo}")
        else:
            # Listar os materiais ativos para depuração
            for material in materiais_ativos:
                print(f"Material ativo: ID={material.id}, Nome={material}")

        return context

    def form_valid(self, form):
        # Verificar se é uma requisição AJAX
        if self.request.headers.get('X-Requested-With') == 'XMLHttpRequest' or self.request.POST.get('ajax') == 'true':
            # Não salvar o formulário, apenas retornar uma resposta vazia
            return JsonResponse({'success': True})

        messages.success(self.request, 'Ordem de fabricação criada com sucesso!')
        return super().form_valid(form)

    def form_invalid(self, form):
        # Verificar se é uma requisição AJAX
        if self.request.headers.get('X-Requested-With') == 'XMLHttpRequest' or self.request.POST.get('ajax') == 'true':
            # Retornar os erros do formulário
            return JsonResponse({'success': False, 'errors': form.errors})

        return super().form_invalid(form)


class PlanejamentoProducaoUpdateView(UpdateView):
    model = PlanejamentoProducao
    form_class = PlanejamentoProducaoForm
    template_name = 'estoque/planejamento/planejamento_form.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Adicionar todos os materiais ativos ao contexto
        materiais_ativos = Material.objects.filter(ativo=True)
        context['todos_materiais'] = materiais_ativos

        # Registrar informações para depuração
        print(f"Total de materiais ativos (update view): {materiais_ativos.count()}")

        # Se não há materiais ativos, incluir todos os materiais como fallback
        if materiais_ativos.count() == 0:
            print("ALERTA: Nenhum material ativo encontrado (update view)!")

            # Verificar se existem materiais no sistema
            total_materiais = Material.objects.all().count()
            print(f"Total de materiais no sistema (update view): {total_materiais}")

            if total_materiais > 0:
                print("Incluindo todos os materiais (mesmo inativos) como fallback (update view)")
                context['todos_materiais'] = Material.objects.all()

                # Listar os materiais para depuração
                for material in context['todos_materiais']:
                    print(f"Material (update view): ID={material.id}, Nome={material}, Ativo={material.ativo}")
        else:
            # Listar os materiais ativos para depuração
            for material in materiais_ativos:
                print(f"Material ativo (update view): ID={material.id}, Nome={material}")

        return context

    def get_success_url(self):
        return reverse_lazy('planejamento-detail', kwargs={'pk': self.object.pk})

    def form_valid(self, form):
        # Verificar se é uma requisição AJAX
        if self.request.headers.get('X-Requested-With') == 'XMLHttpRequest' or self.request.POST.get('ajax') == 'true':
            # Não salvar o formulário, apenas retornar uma resposta vazia
            return JsonResponse({'success': True})

        messages.success(self.request, 'Ordem de fabricação atualizada com sucesso!')
        return super().form_valid(form)

    def form_invalid(self, form):
        # Verificar se é uma requisição AJAX
        if self.request.headers.get('X-Requested-With') == 'XMLHttpRequest' or self.request.POST.get('ajax') == 'true':
            # Retornar os erros do formulário
            return JsonResponse({'success': False, 'errors': form.errors})

        return super().form_invalid(form)


class PlanejamentoProducaoDeleteView(DeleteView):
    model = PlanejamentoProducao
    template_name = 'estoque/planejamento/planejamento_confirm_delete.html'
    success_url = reverse_lazy('planejamento-list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'Ordem de fabricação excluída com sucesso!')
        return super().delete(request, *args, **kwargs)


class ItemPlanejamentoCreateView(CreateView):
    model = ItemPlanejamento
    form_class = ItemPlanejamentoForm
    template_name = 'estoque/planejamento/item_planejamento_form.html'

    def get_initial(self):
        initial = super().get_initial()
        planejamento_id = self.kwargs.get('planejamento_id')
        if planejamento_id:
            initial['planejamento'] = planejamento_id
        return initial

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        planejamento_id = self.kwargs.get('planejamento_id')
        if planejamento_id:
            context['planejamento'] = get_object_or_404(PlanejamentoProducao, pk=planejamento_id)
        return context

    def get_success_url(self):
        return reverse_lazy('planejamento-detail', kwargs={'pk': self.object.planejamento.pk})

    def form_valid(self, form):
        messages.success(self.request, 'Item adicionado à ordem de fabricação com sucesso!')
        return super().form_valid(form)


class ItemPlanejamentoUpdateView(UpdateView):
    model = ItemPlanejamento
    form_class = ItemPlanejamentoForm
    template_name = 'estoque/planejamento/item_planejamento_form.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['planejamento'] = self.object.planejamento
        return context

    def get_success_url(self):
        return reverse_lazy('planejamento-detail', kwargs={'pk': self.object.planejamento.pk})

    def form_valid(self, form):
        messages.success(self.request, 'Item da ordem de fabricação atualizado com sucesso!')
        return super().form_valid(form)


class ItemPlanejamentoDeleteView(DeleteView):
    model = ItemPlanejamento
    template_name = 'estoque/planejamento/item_planejamento_confirm_delete.html'

    def get_success_url(self):
        return reverse_lazy('planejamento-detail', kwargs={'pk': self.object.planejamento.pk})

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'Item removido da ordem de fabricação com sucesso!')
        return super().delete(request, *args, **kwargs)


def planejamento_automatico(request):
    """View para gerar ordens de fabricação automáticas"""
    if request.method == 'POST':
        form = PlanejamentoAutomaticoForm(request.POST)
        if form.is_valid():
            data_inicio = form.cleaned_data['data_inicio']
            dias_duracao = form.cleaned_data['dias_duracao']
            pedidos_ids = form.cleaned_data['pedidos']
            previsoes_ids = form.cleaned_data['previsoes']

            # Gerar a ordem
            ordem = PlanejamentoProducao.gerar_plano_automatico(
                data_inicio=data_inicio,
                dias_duracao=dias_duracao,
                pedidos_ids=pedidos_ids,
                previsoes_ids=previsoes_ids
            )

            if ordem:
                messages.success(request, f'Ordem de fabricação "{ordem.nome}" gerada com sucesso!')
                return redirect('planejamento-detail', pk=ordem.pk)
            else:
                messages.error(request, 'Não foi possível gerar a ordem de fabricação. Verifique os dados informados.')
    else:
        form = PlanejamentoAutomaticoForm()

    # Obter pedidos pendentes
    pedidos_pendentes = PedidoVenda.objects.filter(status='P')

    # Obter previsões de demanda
    hoje = timezone.now().date()
    previsoes = PrevisaoDemanda.objects.filter(data_fim__gte=hoje)

    context = {
        'form': form,
        'pedidos_pendentes': pedidos_pendentes,
        'previsoes': previsoes
    }

    return render(request, 'estoque/planejamento/planejamento_automatico.html', context)


def registrar_producao(request, pk):
    """View para registrar produção de um item"""
    item = get_object_or_404(ItemPlanejamento, pk=pk)

    if request.method == 'POST':
        form = RegistroProducaoForm(request.POST)
        if form.is_valid():
            quantidade = form.cleaned_data['quantidade']

            # Registrar produção
            sucesso = item.registrar_producao(quantidade)

            if sucesso:
                messages.success(request, f'Produção de {quantidade} unidades registrada com sucesso!')
            else:
                messages.error(request, 'Não foi possível registrar a produção. O item já está concluído.')

            return redirect('planejamento-detail', pk=item.planejamento.pk)

    return redirect('planejamento-detail', pk=item.planejamento.pk)


def iniciar_producao(request, pk):
    """View para iniciar a produção de um item"""
    item = get_object_or_404(ItemPlanejamento, pk=pk)

    sucesso = item.iniciar_producao()

    if sucesso:
        messages.success(request, f'Produção do item {item.mola.codigo} iniciada com sucesso!')
    else:
        messages.error(request, 'Não foi possível iniciar a produção. Verifique o status do item.')

    return redirect('planejamento-detail', pk=item.planejamento.pk)


def concluir_planejamento(request, pk):
    """View para concluir uma ordem de fabricação"""
    planejamento = get_object_or_404(PlanejamentoProducao, pk=pk)

    if planejamento.status in ['P', 'E', 'A']:
        planejamento.status = 'C'
        planejamento.data_fim_real = timezone.now().date()
        planejamento.save(update_fields=['status', 'data_fim_real'])

        messages.success(request, f'Ordem de fabricação "{planejamento.nome}" concluída com sucesso!')
    else:
        messages.error(request, 'Não foi possível concluir a ordem de fabricação. Verifique o status.')

    return redirect('planejamento-detail', pk=planejamento.pk)


def cancelar_planejamento(request, pk):
    """View para cancelar uma ordem de fabricação"""
    planejamento = get_object_or_404(PlanejamentoProducao, pk=pk)

    if planejamento.status in ['P', 'E', 'A']:
        planejamento.status = 'X'
        planejamento.save(update_fields=['status'])

        messages.success(request, f'Ordem de fabricação "{planejamento.nome}" cancelada com sucesso!')
    else:
        messages.error(request, 'Não foi possível cancelar a ordem de fabricação. Verifique o status.')

    return redirect('planejamento-detail', pk=planejamento.pk)


def finalizar_ordem_fabricacao(request, pk):
    """View para finalizar uma ordem de fabricação"""
    planejamento = get_object_or_404(PlanejamentoProducao, pk=pk)

    # Verificar se a ordem já está concluída
    if planejamento.status == 'C':
        messages.warning(request, f'A ordem de fabricação "{planejamento.nome}" já está concluída.')
        return redirect('planejamento-detail', pk=planejamento.pk)

    # Obter o item de planejamento associado
    try:
        item = planejamento.itens.first()
    except ItemPlanejamento.DoesNotExist:
        messages.error(request, 'Não foi possível encontrar o item associado a esta ordem de fabricação.')
        return redirect('planejamento-detail', pk=planejamento.pk)

    # Usar a quantidade definida na ordem como quantidade produzida
    quantidade_produzida = item.quantidade

    # Atualizar o item de planejamento
    item.quantidade_produzida = quantidade_produzida
    item.status = 'C'
    item.data_conclusao = timezone.now().date()
    item.save()

    # Atualizar o planejamento
    planejamento.status = 'C'
    planejamento.data_fim_real = timezone.now().date()
    planejamento.save()

    # Adicionar ao estoque
    MovimentacaoEstoque.objects.create(
        mola=item.mola,
        tipo='E',
        quantidade=quantidade_produzida,
        observacao=f"Produção da O.F. #{planejamento.nome}"
    )

    # Descontar material utilizado
    mola = item.mola

    # Usar o material selecionado na ordem de fabricação, se disponível
    # Caso contrário, usar o material da mola
    material = planejamento.material or mola.material

    # Verificar se há material associado
    if material:
        # Calcular quantidade de material utilizado
        if mola.peso_unitario:
            # Converter de gramas para quilogramas (dividir por 1000)
            peso_por_mola = mola.peso_unitario / Decimal('1000')
        else:
            # Usar valor padrão se não houver peso unitário informado (1g = 0.001kg)
            peso_por_mola = Decimal('0.001')

        material_utilizado = Decimal(str(quantidade_produzida)) * peso_por_mola

        # Verificar se há estoque suficiente
        if material.quantidade_estoque >= material_utilizado:
            # Registrar saída de material
            MovimentacaoMaterial.objects.create(
                material=material,
                tipo='S',
                quantidade=material_utilizado,
                observacao=f"Material utilizado na O.F. #{planejamento.nome}"
            )
        else:
            messages.warning(request, f'Material insuficiente em estoque. Necessário: {material_utilizado:.2f} kg, Disponível: {material.quantidade_estoque:.2f} kg. A ordem foi finalizada, mas o material não foi descontado.')
    else:
        messages.warning(request, 'Nenhum material associado à mola ou à ordem de fabricação. A ordem foi finalizada, mas nenhum material foi descontado.')

    messages.success(request, f'Ordem de fabricação "{planejamento.nome}" finalizada com sucesso! {quantidade_produzida} unidades produzidas.')
    return redirect('planejamento-list')


def dashboard_producao(request):
    """Dashboard de produção e ordens de fabricação"""
    # Obter parâmetros de filtro
    periodo = request.GET.get('periodo', '30')

    try:
        dias = int(periodo)
    except ValueError:
        dias = 30

    # Calcular datas
    hoje = timezone.now().date()
    data_inicio = hoje - timedelta(days=dias)

    # Estatísticas de ordens de fabricação
    total_planejamentos = PlanejamentoProducao.objects.count()
    planejamentos_ativos = PlanejamentoProducao.objects.filter(status__in=['P', 'E']).count()
    planejamentos_concluidos = PlanejamentoProducao.objects.filter(status='C').count()
    planejamentos_atrasados = PlanejamentoProducao.objects.filter(status='A').count()

    # Ordens de fabricação recentes
    planejamentos_recentes = PlanejamentoProducao.objects.all().order_by('-data_criacao')[:5]

    # Itens em produção
    itens_em_producao = ItemPlanejamento.objects.filter(status='E').order_by('-prioridade')[:10]

    # Calcular taxa de conclusão no prazo
    planejamentos_periodo = PlanejamentoProducao.objects.filter(
        data_fim_real__isnull=False,
        data_criacao__gte=data_inicio
    )

    total_periodo = planejamentos_periodo.count()
    no_prazo = planejamentos_periodo.filter(data_fim_real__lte=F('data_fim_prevista')).count()

    taxa_no_prazo = (no_prazo / total_periodo * 100) if total_periodo > 0 else 0

    # Dados para gráficos

    # 1. Ordens de fabricação por status
    status_counts = {
        'Pendente': PlanejamentoProducao.objects.filter(status='P').count(),
        'Em Produção': PlanejamentoProducao.objects.filter(status='E').count(),
        'Concluído': PlanejamentoProducao.objects.filter(status='C').count(),
        'Atrasado': PlanejamentoProducao.objects.filter(status='A').count(),
        'Cancelado': PlanejamentoProducao.objects.filter(status='X').count()
    }

    # 2. Produção por mês (últimos 6 meses)
    producao_mensal = []
    for i in range(5, -1, -1):
        mes_inicio = hoje.replace(day=1) - timedelta(days=i*30)
        mes_fim = (mes_inicio.replace(day=28) + timedelta(days=4)).replace(day=1) - timedelta(days=1)

        # Contar itens concluídos no mês
        itens_concluidos = ItemPlanejamento.objects.filter(
            data_conclusao__gte=mes_inicio,
            data_conclusao__lte=mes_fim,
            status='C'
        )

        # Somar quantidade produzida
        quantidade = itens_concluidos.aggregate(total=Sum('quantidade_produzida'))['total'] or 0

        producao_mensal.append({
            'mes': mes_inicio.strftime('%b/%Y'),
            'quantidade': quantidade
        })

    context = {
        'periodo': periodo,
        'total_planejamentos': total_planejamentos,
        'planejamentos_ativos': planejamentos_ativos,
        'planejamentos_concluidos': planejamentos_concluidos,
        'planejamentos_atrasados': planejamentos_atrasados,
        'taxa_no_prazo': taxa_no_prazo,
        'planejamentos_recentes': planejamentos_recentes,
        'itens_em_producao': itens_em_producao,
        'status_counts': json.dumps(list(status_counts.values())),
        'status_labels': json.dumps(list(status_counts.keys())),
        'producao_mensal': json.dumps([item['quantidade'] for item in producao_mensal]),
        'meses_labels': json.dumps([item['mes'] for item in producao_mensal])
    }

    return render(request, 'estoque/planejamento/dashboard_producao.html', context)


def get_material_by_mola(request, mola_id):
    """View para obter os materiais associados a uma mola via AJAX"""
    try:
        # Buscar a mola pelo ID com select_related para otimizar consultas
        mola = Mola.objects.select_related('material', 'material_padrao').get(pk=mola_id)

        # Inicializar variáveis
        material_mola_id = mola.material.id if mola.material else None
        material_padrao_id = mola.material_padrao.id if mola.material_padrao else None
        material_padrao_nome = str(mola.material_padrao) if mola.material_padrao else None
        materiais = Material.objects.none()

        # Verificar se a mola tem material padrão
        if mola.material_padrao:
            # Buscar todos os materiais que são variantes desse material padrão
            # Primeiro tentar com estoque disponível
            materiais = Material.objects.filter(
                material_padrao=mola.material_padrao,
                ativo=True,
                quantidade_estoque__gt=0
            ).only('id', 'nome', 'diametro')

            # Se não encontrou materiais com estoque, mostrar todos os variantes ativos
            if not materiais.exists():
                materiais = Material.objects.filter(
                    material_padrao=mola.material_padrao,
                    ativo=True
                ).only('id', 'nome', 'diametro')

        # Se não encontrou materiais pelo material padrão, mas tem material específico
        if not materiais.exists() and mola.material and mola.material.ativo:
            materiais = Material.objects.filter(pk=mola.material.pk).only('id', 'nome', 'diametro')

        # Se ainda não encontrou materiais, retornar todos os materiais ativos
        if not materiais.exists():
            materiais = Material.objects.filter(ativo=True).only('id', 'nome', 'diametro')

        # Se ainda não encontrou materiais, retornar todos os materiais (mesmo inativos)
        if not materiais.exists():
            materiais = Material.objects.all().only('id', 'nome', 'diametro')

        # Formatar os materiais para retornar no JSON
        materiais_json = [{'id': material.id, 'nome': str(material), 'diametro': material.diametro} for material in materiais]

        # Ordenar materiais por nome e diâmetro numérico
        materiais_json = ordenar_materiais(materiais_json)

        response_data = {
            'success': True,
            'materiais': materiais_json,
            'material_mola_id': material_mola_id,
            'material_padrao_id': material_padrao_id,
            'material_padrao_nome': material_padrao_nome,
            'tem_materiais_especificos': materiais.exists()
        }

        return JsonResponse(response_data)
    except Mola.DoesNotExist:
        return JsonResponse({
            'success': False,
            'error': 'Mola não encontrada'
        }, status=404)
    except Exception as e:
        import traceback
        traceback.print_exc()
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)
