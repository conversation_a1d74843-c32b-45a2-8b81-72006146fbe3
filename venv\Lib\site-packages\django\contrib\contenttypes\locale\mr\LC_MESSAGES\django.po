# This file is distributed under the same license as the Django package.
#
# Translators:
# Omkar Parab, 2024
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2024-01-24 19:22+0000\n"
"Last-Translator: Omkar Parab, 2024\n"
"Language-Team: Marathi (http://app.transifex.com/django/django/language/"
"mr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: mr\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Content Types"
msgstr "सामुग्रीचे प्रकार"

msgid "python model class name"
msgstr "पायथन मॉडेल वर्गाचे नाव"

msgid "content type"
msgstr "सामुग्री प्रकार"

msgid "content types"
msgstr "सामुग्रीचे प्रकार"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "%(ct_id)s या सामुग्री प्रकाराच्या ऑब्जेक्टला संबंधित मॉडेल नाही."

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn’t exist"
msgstr ""

#, python-format
msgid "%(ct_name)s objects don’t have a get_absolute_url() method"
msgstr ""
